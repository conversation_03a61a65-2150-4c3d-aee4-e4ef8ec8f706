@extends('layouts.app')

@section('title', 'Notifications')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Notifications</h1>
                    <p class="text-gray-600 mt-1">Stay updated with your delivery activities and important updates</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('customer.dashboard') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                    @if($notifications->where('read_at', null)->count() > 0)
                        <button onclick="markAllAsRead()" 
                                class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-check-double mr-2"></i>Mark All Read
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- Notification Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <i class="fas fa-bell text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Notifications</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $notifications->total() }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-orange-100">
                            <i class="fas fa-envelope text-orange-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Unread</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $notifications->where('read_at', null)->count() }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <i class="fas fa-envelope-open text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Read</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $notifications->where('read_at', '!=', null)->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">All Notifications</h3>
                </div>
                
                @if($notifications->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($notifications as $notification)
                            <div class="p-6 hover:bg-gray-50 transition-colors {{ $notification->isUnread() ? 'bg-blue-50' : '' }}" 
                                 data-notification-id="{{ $notification->id }}">
                                <div class="flex items-start space-x-4">
                                    <!-- Notification Icon -->
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 rounded-full flex items-center justify-center {{ $notification->color === 'green' ? 'bg-green-100' : ($notification->color === 'red' ? 'bg-red-100' : ($notification->color === 'yellow' ? 'bg-yellow-100' : 'bg-blue-100')) }}">
                                            <i class="{{ $notification->icon }} {{ $notification->color === 'green' ? 'text-green-600' : ($notification->color === 'red' ? 'text-red-600' : ($notification->color === 'yellow' ? 'text-yellow-600' : 'text-blue-600')) }}"></i>
                                        </div>
                                    </div>
                                    
                                    <!-- Notification Content -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class="text-sm font-semibold text-gray-900 {{ $notification->isUnread() ? 'font-bold' : '' }}">
                                                    {{ $notification->title }}
                                                    @if($notification->isUnread())
                                                        <span class="inline-block w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                                                    @endif
                                                </h4>
                                                <p class="text-sm text-gray-600 mt-1">{{ $notification->message }}</p>
                                                
                                                <!-- Additional Data -->
                                                @if($notification->data && isset($notification->data['booking_id']))
                                                    <div class="mt-2">
                                                        <a href="{{ route('booking.show', $notification->data['booking_id']) }}" 
                                                           class="inline-flex items-center text-xs text-orange-600 hover:text-orange-800 font-medium">
                                                            <i class="fas fa-external-link-alt mr-1"></i>
                                                            View Booking
                                                        </a>
                                                    </div>
                                                @endif
                                            </div>
                                            
                                            <!-- Actions -->
                                            <div class="flex items-center space-x-2 ml-4">
                                                @if($notification->isUnread())
                                                    <button onclick="markAsRead('{{ $notification->id }}')" 
                                                            class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                                                        Mark as Read
                                                    </button>
                                                @endif
                                                <span class="text-xs text-gray-500">
                                                    {{ $notification->created_at->diffForHumans() }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    @if($notifications->hasPages())
                        <div class="p-6 border-t border-gray-200">
                            {{ $notifications->links() }}
                        </div>
                    @endif
                @else
                    <!-- Empty State -->
                    <div class="p-12 text-center">
                        <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-bell-slash text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications yet</h3>
                        <p class="text-gray-600 mb-6">You'll receive notifications about your bookings and account updates here.</p>
                        <a href="{{ route('booking.create') }}" 
                           class="inline-flex items-center px-6 py-3 brand-orange text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-plus mr-2"></i>Create Your First Booking
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
// Mark single notification as read
function markAsRead(notificationId) {
    fetch(`/customer/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the notification appearance
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.remove('bg-blue-50');
                
                // Remove the unread indicator
                const unreadIndicator = notificationElement.querySelector('.bg-blue-500');
                if (unreadIndicator) {
                    unreadIndicator.remove();
                }
                
                // Remove the "Mark as Read" button
                const markAsReadBtn = notificationElement.querySelector('button[onclick*="markAsRead"]');
                if (markAsReadBtn) {
                    markAsReadBtn.remove();
                }
                
                // Update font weight
                const title = notificationElement.querySelector('h4');
                if (title) {
                    title.classList.remove('font-bold');
                }
            }
            
            // Update counters
            updateNotificationCounters();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

// Mark all notifications as read
function markAllAsRead() {
    const unreadNotifications = document.querySelectorAll('[data-notification-id]');
    const unreadIds = Array.from(unreadNotifications)
        .filter(el => el.classList.contains('bg-blue-50'))
        .map(el => el.getAttribute('data-notification-id'));
    
    if (unreadIds.length === 0) {
        return;
    }
    
    // Mark each notification as read
    unreadIds.forEach(id => {
        markAsRead(id);
    });
}

// Update notification counters
function updateNotificationCounters() {
    const unreadCount = document.querySelectorAll('.bg-blue-50').length;
    const totalCount = document.querySelectorAll('[data-notification-id]').length;
    const readCount = totalCount - unreadCount;
    
    // Update counter displays if they exist
    const counters = document.querySelectorAll('.text-2xl.font-bold.text-gray-900');
    if (counters.length >= 3) {
        counters[1].textContent = unreadCount; // Unread counter
        counters[2].textContent = readCount;   // Read counter
    }
    
    // Hide "Mark All Read" button if no unread notifications
    if (unreadCount === 0) {
        const markAllBtn = document.querySelector('button[onclick="markAllAsRead()"]');
        if (markAllBtn) {
            markAllBtn.style.display = 'none';
        }
    }
}
</script>
@endsection
