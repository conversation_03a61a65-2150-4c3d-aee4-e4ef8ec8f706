<?php

namespace App\Services;

use App\Models\Booking;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class RouteOptimizationService
{
    protected $googleMapsApiKey;

    public function __construct()
    {
        $this->googleMapsApiKey = config('services.google.maps_api_key');
    }

    /**
     * Calculate optimal route and ETA for a booking
     */
    public function calculateRoute(Booking $booking): array
    {
        try {
            $cacheKey = $this->generateCacheKey($booking);

            return Cache::remember($cacheKey, 300, function () use ($booking) {
                return $this->fetchRouteFromGoogleMaps($booking);
            });
        } catch (\Exception $e) {
            Log::error("Route calculation failed for booking {$booking->booking_id}: " . $e->getMessage());
            return $this->getFallbackRoute($booking);
        }
    }

    /**
     * Fetch route data from Google Maps API
     */
    protected function fetchRouteFromGoogleMaps(Booking $booking): array
    {
        // Route from pickup to delivery
        $origin = "{$booking->pickup_latitude},{$booking->pickup_longitude}";
        $destination = "{$booking->delivery_latitude},{$booking->delivery_longitude}";

        $url = 'https://maps.googleapis.com/maps/api/directions/json';
        $params = [
            'origin' => $origin,
            'destination' => $destination,
            'mode' => 'driving',
            'traffic_model' => 'best_guess',
            'departure_time' => 'now',
            'key' => $this->googleMapsApiKey,
        ];

        $response = Http::get($url, $params);

        if ($response->successful() && $response->json('status') === 'OK') {
            return $this->parseGoogleMapsResponse($response->json(), $booking);
        }

        throw new \Exception('Google Maps API request failed: ' . $response->json('status', 'Unknown error'));
    }

    /**
     * Parse Google Maps API response
     */
    protected function parseGoogleMapsResponse(array $data, Booking $booking): array
    {
        $route = $data['routes'][0];
        $legs = $route['legs'];

        $totalDistance = 0;
        $totalDuration = 0;
        $totalDurationInTraffic = 0;

        foreach ($legs as $leg) {
            $totalDistance += $leg['distance']['value'];
            $totalDuration += $leg['duration']['value'];
            $totalDurationInTraffic += $leg['duration_in_traffic']['value'] ?? $leg['duration']['value'];
        }

        $result = [
            'total_distance_km' => round($totalDistance / 1000, 2),
            'total_duration_minutes' => round($totalDuration / 60),
            'duration_in_traffic_minutes' => round($totalDurationInTraffic / 60),
            'polyline' => $route['overview_polyline']['points'],
            'legs' => [],
        ];

        // Parse individual legs
        foreach ($legs as $index => $leg) {
            $legData = [
                'distance_km' => round($leg['distance']['value'] / 1000, 2),
                'duration_minutes' => round($leg['duration']['value'] / 60),
                'duration_in_traffic_minutes' => round(($leg['duration_in_traffic']['value'] ?? $leg['duration']['value']) / 60),
                'start_address' => $leg['start_address'],
                'end_address' => $leg['end_address'],
                'steps' => $this->parseSteps($leg['steps']),
            ];

            $legData['type'] = 'pickup_to_delivery';
            $result['eta_to_delivery'] = now()->addMinutes($legData['duration_in_traffic_minutes']);

            $result['legs'][] = $legData;
        }

        return $result;
    }

    /**
     * Parse route steps
     */
    protected function parseSteps(array $steps): array
    {
        return array_map(function ($step) {
            return [
                'distance_km' => round($step['distance']['value'] / 1000, 2),
                'duration_minutes' => round($step['duration']['value'] / 60),
                'instruction' => strip_tags($step['html_instructions']),
                'maneuver' => $step['maneuver'] ?? null,
            ];
        }, $steps);
    }

    /**
     * Get fallback route calculation
     */
    protected function getFallbackRoute(Booking $booking): array
    {
        $distance = $this->calculateStraightLineDistance(
            $booking->pickup_latitude,
            $booking->pickup_longitude,
            $booking->delivery_latitude,
            $booking->delivery_longitude
        );

        $estimatedDuration = max(15, $distance * 3); // Rough estimate: 3 minutes per km



        return [
            'total_distance_km' => $distance,
            'total_duration_minutes' => $estimatedDuration,
            'duration_in_traffic_minutes' => $estimatedDuration * 1.2, // Add 20% for traffic
            'eta_to_delivery' => now()->addMinutes($estimatedDuration * 1.2),
            'fallback' => true,
        ];
    }

    /**
     * Calculate straight-line distance between two points
     */
    protected function calculateStraightLineDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Generate cache key for route data
     */
    protected function generateCacheKey(Booking $booking): string
    {
        return "route_booking_{$booking->id}";
    }

    /**
     * Update ETA for active bookings
     */
    public function updateETAForActiveBookings(): void
    {
        $activeBookings = Booking::whereIn('status', ['in_progress'])
            ->get();

        foreach ($activeBookings as $booking) {
            $routeData = $this->calculateRoute($booking);

            // Update booking with new ETA
            $booking->update([
                'estimated_duration_minutes' => $routeData['duration_in_traffic_minutes'],
                'distance_km' => $routeData['total_distance_km'],
            ]);
        }
    }

    /**
     * Get route optimization statistics
     */
    public function getOptimizationStats(): array
    {
        return [
            'total_routes_calculated' => Cache::get('routes_calculated_count', 0),
            'average_calculation_time' => Cache::get('average_calculation_time', 0),
            'cache_hit_rate' => Cache::get('route_cache_hit_rate', 0),
            'api_calls_today' => Cache::get('google_maps_calls_today', 0),
        ];
    }
}
