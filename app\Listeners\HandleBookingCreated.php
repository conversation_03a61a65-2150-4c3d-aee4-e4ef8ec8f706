<?php

namespace App\Listeners;

use App\Events\BookingCreated;
use App\Services\RouteOptimizationService;
use App\Jobs\CalculateRouteJob;
use App\Jobs\SendNotificationJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleBookingCreated implements ShouldQueue
{
    use InteractsWithQueue;

    protected $routeOptimizationService;

    /**
     * Create the event listener.
     */
    public function __construct(
        RouteOptimizationService $routeOptimizationService
    ) {
        $this->routeOptimizationService = $routeOptimizationService;
    }

    /**
     * Handle the event.
     */
    public function handle(BookingCreated $event): void
    {
        $booking = $event->booking;

        Log::info("Processing new booking: {$booking->booking_id}");

        try {
            // 1. Confirm the booking automatically
            $this->confirmBooking($booking);

            // 2. Calculate initial route and cost estimation
            $this->calculateInitialRoute($booking);



            // 4. Send confirmation notifications
            $this->sendConfirmationNotifications($booking);

            // 5. Update admin dashboard
            $this->updateDashboards($booking);

        } catch (\Exception $e) {
            Log::error("Failed to process booking {$booking->booking_id}: " . $e->getMessage());
            $this->handleProcessingFailure($booking, $e);
        }
    }

    /**
     * Automatically confirm the booking
     */
    protected function confirmBooking($booking): void
    {
        $booking->update(['status' => 'confirmed']);
        
        Log::info("Booking {$booking->booking_id} automatically confirmed");
    }

    /**
     * Calculate initial route for cost estimation
     */
    protected function calculateInitialRoute($booking): void
    {
        // Dispatch route calculation job
        CalculateRouteJob::dispatch($booking);
    }



    /**
     * Send confirmation notifications
     */
    protected function sendConfirmationNotifications($booking): void
    {
        // Customer confirmation notification
        $customerNotification = [
            'user_id' => $booking->user_id,
            'title' => 'Booking Confirmed',
            'message' => "Your booking {$booking->booking_id} has been confirmed and will be processed shortly.",
            'type' => 'booking',
            'data' => [
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_id,
                'status' => 'confirmed',
            ],
        ];

        SendNotificationJob::dispatch($customerNotification);

        // Admin notification for new booking
        $adminNotification = [
            'role' => 'admin',
            'title' => 'New Booking Created',
            'message' => "New booking {$booking->booking_id} created by {$booking->customer->name}",
            'type' => 'booking',
            'data' => [
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_id,
                'customer_name' => $booking->customer->name,
            ],
        ];

        SendNotificationJob::dispatch($adminNotification);
    }

    /**
     * Update dashboards with new booking data
     */
    protected function updateDashboards($booking): void
    {
        // This would trigger real-time updates to admin dashboard
        // The broadcasting is already handled by the BookingCreated event
        
        Log::info("Dashboard updates triggered for booking {$booking->booking_id}");
    }

    /**
     * Handle processing failure
     */
    protected function handleProcessingFailure($booking, \Exception $e): void
    {
        // Log the error
        Log::error("Booking processing failed for {$booking->booking_id}: " . $e->getMessage());

        // Create error notification for admins
        $errorNotification = [
            'role' => 'admin',
            'title' => 'Booking Processing Error',
            'message' => "Failed to process booking {$booking->booking_id}. Manual intervention required.",
            'type' => 'system',
            'data' => [
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_id,
                'error' => $e->getMessage(),
                'requires_manual_action' => true,
            ],
        ];

        SendNotificationJob::dispatch($errorNotification);

        // Mark booking for manual review
        $booking->update([
            'status' => 'pending',
            'special_instructions' => ($booking->special_instructions ?? '') . 
                "\n\nAUTO-PROCESSING FAILED: " . $e->getMessage()
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(BookingCreated $event, \Throwable $exception): void
    {
        Log::error("BookingCreated listener failed for booking {$event->booking->booking_id}: " . $exception->getMessage());
        
        // Send critical error notification
        $criticalNotification = [
            'role' => 'admin',
            'title' => 'Critical: Booking Listener Failed',
            'message' => "Critical failure in booking processing for {$event->booking->booking_id}",
            'type' => 'system',
            'data' => [
                'booking_id' => $event->booking->id,
                'error' => $exception->getMessage(),
                'severity' => 'critical',
            ],
        ];

        SendNotificationJob::dispatch($criticalNotification);
    }
}
