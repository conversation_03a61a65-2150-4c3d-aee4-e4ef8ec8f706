@echo off
echo Fixing Laravel directory permissions for Windows...

REM Create directories if they don't exist
if not exist "bootstrap\cache" mkdir "bootstrap\cache"
if not exist "storage\app" mkdir "storage\app"
if not exist "storage\app\public" mkdir "storage\app\public"
if not exist "storage\framework" mkdir "storage\framework"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\cache\data" mkdir "storage\framework\cache\data"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\logs" mkdir "storage\logs"

REM Set permissions (Windows equivalent)
icacls "bootstrap\cache" /grant Everyone:(OI)(CI)F /T
icacls "storage" /grant Everyone:(OI)(CI)F /T

echo.
echo Directory permissions have been set.
echo You can now run: php artisan key:generate
echo.
pause
