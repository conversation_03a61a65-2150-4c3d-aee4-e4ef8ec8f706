@extends('layouts.app')

@section('title', 'Live Map - Delivery Tracking')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Live Delivery Map</h1>
                    <p class="text-gray-600 mt-1">Real-time tracking of all active deliveries</p>
                </div>
                
                <!-- Controls -->
                <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search bookings, addresses..."
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <select id="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="active">Active Deliveries</option>
                        <option value="all">All Deliveries</option>
                        <option value="pending">Pending Assignment</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="delivery_enroute">Delivery Enroute</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    
                    <!-- Refresh Button -->
                    <button onclick="refreshMap()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    
                    <!-- Auto-refresh Toggle -->
                    <label class="flex items-center">
                        <input type="checkbox" id="autoRefresh" checked class="mr-2">
                        <span class="text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-6">
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-truck text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Deliveries</p>
                        <p class="text-2xl font-bold text-gray-900" id="activeCount">{{ $stats['total_active'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Assignment</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingCount">{{ $stats['pending_assignment'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-route text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">In Transit</p>
                        <p class="text-2xl font-bold text-gray-900" id="transitCount">{{ $stats['in_transit'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Completed Today</p>
                        <p class="text-2xl font-bold text-gray-900" id="completedCount">{{ $stats['completed_today'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map Container -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Live Delivery Map</h3>
                            <div class="flex space-x-2">
                                <button onclick="toggleDeliveries()" id="deliveriesToggle" class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                                    <i class="fas fa-box mr-1"></i>Hide Deliveries
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-0">
                        <div id="map" style="height: 600px; width: 100%;" class="flex items-center justify-center bg-gray-200">
                            <div id="map-placeholder">
                                <i class="fas fa-spinner fa-spin text-4xl text-gray-500"></i>
                                <p class="mt-4 text-gray-600">Loading map...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Delivery List -->
            <div class="space-y-6">
                
                <!-- Active Deliveries Summary -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Delivery Overview</h3>
                        <p class="text-sm text-gray-600">{{ $bookings->count() }} active deliveries</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $bookings->where('status', 'confirmed')->count() }}</div>
                                <div class="text-sm text-gray-500">Confirmed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">{{ $bookings->where('status', 'in_progress')->count() }}</div>
                                <div class="text-sm text-gray-500">In Progress</div>
                            </div>
                        </div>
                    </div>

                
                <!-- Active Deliveries List -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Active Deliveries</h3>
                        <p class="text-sm text-gray-600" id="deliveryCount">{{ count($bookings) }} deliveries</p>
                    </div>
                    <div class="max-h-96 overflow-y-auto" id="deliveryList">
                        @forelse($bookings as $booking)
                            <div class="p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer delivery-item" 
                                 data-booking-id="{{ $booking->id }}"
                                 onclick="focusOnDelivery({{ $booking->id }})">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <span class="text-sm font-medium text-gray-900">{{ $booking->booking_id }}</span>
                                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                @switch($booking->status)
                                                    @case('confirmed')
                                                        bg-blue-100 text-blue-800
                                                        @break

                                                    @case('in_progress')
                                                        bg-purple-100 text-purple-800
                                                        @break
                                                    @default
                                                        bg-gray-100 text-gray-800
                                                @endswitch
                                            ">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                                                <span class="truncate">{{ Str::limit($booking->pickup_address, 30) }}</span>
                                            </div>
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                                                <span class="truncate">{{ Str::limit($booking->delivery_address, 30) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-2">
                                        <button onclick="event.stopPropagation(); viewBookingDetails({{ $booking->id }})" 
                                                class="text-orange-600 hover:text-orange-800 text-xs">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="p-6 text-center text-gray-500">
                                <i class="fas fa-box-open text-2xl mb-2"></i>
                                <p>No active deliveries</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Details Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Booking Details</h3>
                <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Google Maps API Key from environment
const GOOGLE_MAPS_API_KEY = '{{ config("services.google.maps_api_key", env("GOOGLE_MAPS_API_KEY")) }}';

// Global variables
let map;
let markers = {
    deliveries: []
};
let showDeliveries = true;
let autoRefreshInterval;

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if Google Maps API is already loaded
    if (typeof google === 'object' && typeof google.maps === 'object') {
        initializeMap();
    } else {
        // Load Google Maps API script dynamically
        loadGoogleMapsScript();
    }
    setupEventListeners();
    startAutoRefresh();
});

// Make initializeMap globally accessible
window.initializeMap = initializeMap;

// Initialize Google Map
function initializeMap() {
    // Default center (Accra, Ghana)
    const defaultCenter = { lat: 5.6037, lng: -0.1870 };
    
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: defaultCenter,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            }
        ]
    });
    
    loadMapData();
}

// Load all map data
function loadMapData() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/api/live-map/deliveries', {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        }
    })
        .then(response => {
            if (response.status === 401) {
                throw new Error('Authentication failed. Please log in.');
            }
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(deliveries => {
            document.getElementById('map-placeholder').style.display = 'none';
            clearMarkers();
            addDeliveryMarkers(deliveries);
        }).catch(error => {
            console.error('Error loading map data:', error);
            const mapDiv = document.getElementById('map');
            mapDiv.innerHTML = `
                <div class="text-center text-red-500">
                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                    <p class="mt-4">Failed to load map data.</p>
                    <p class="text-sm text-gray-600">${error.message}</p>
                </div>
            `;
        });
}

// Add delivery markers to map
function addDeliveryMarkers(deliveries) {
    deliveries.forEach(delivery => {
        // Pickup marker
        const pickupMarker = new google.maps.Marker({
            position: { lat: delivery.pickup.latitude, lng: delivery.pickup.longitude },
            map: showDeliveries ? map : null,
            title: `Pickup: ${delivery.booking_id}`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                        <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(24, 24)
            }
        });
        
        // Delivery marker
        const deliveryMarker = new google.maps.Marker({
            position: { lat: delivery.delivery.latitude, lng: delivery.delivery.longitude },
            map: showDeliveries ? map : null,
            title: `Delivery: ${delivery.booking_id}`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                        <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(24, 24)
            }
        });
        

        
        // Info windows
        const pickupInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">${delivery.booking_id} - Pickup</h4>
                    <p class="text-sm">${delivery.pickup.address}</p>
                    <p class="text-xs text-gray-600">Status: ${delivery.status}</p>
                </div>
            `
        });
        
        const deliveryInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">${delivery.booking_id} - Delivery</h4>
                    <p class="text-sm">${delivery.delivery.address}</p>
                    <p class="text-xs text-gray-600">Status: ${delivery.status}</p>
                </div>
            `
        });
        
        pickupMarker.addListener('click', () => {
            pickupInfoWindow.open(map, pickupMarker);
        });
        
        deliveryMarker.addListener('click', () => {
            deliveryInfoWindow.open(map, deliveryMarker);
        });
        
        markers.deliveries.push(pickupMarker, deliveryMarker);
    });
}



        


// Clear all markers
function clearMarkers() {
    markers.deliveries.forEach(marker => {
        marker.setMap(null);
    });
    markers.deliveries = [];
}



// Toggle deliveries visibility
function toggleDeliveries() {
    showDeliveries = !showDeliveries;
    markers.deliveries.forEach(marker => {
        marker.setMap(showDeliveries ? map : null);
    });
    
    const button = document.getElementById('deliveriesToggle');
    button.innerHTML = showDeliveries ? 
        '<i class="fas fa-box mr-1"></i>Hide Deliveries' : 
        '<i class="fas fa-box mr-1"></i>Show Deliveries';
    button.className = showDeliveries ? 
        'px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full' : 
        'px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full';
}

// Focus on specific delivery
function focusOnDelivery(bookingId) {
    // This would focus the map on the specific delivery
    // Implementation depends on having the booking data available
    console.log('Focus on delivery:', bookingId);
}


// View booking details
function viewBookingDetails(bookingId) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch(`/api/live-map/booking/${bookingId}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        }
    })
        .then(response => response.json())
        .then(data => {
            document.getElementById('modalTitle').textContent = `Booking ${data.booking_id}`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-900">Pickup</h4>
                            <p class="text-sm text-gray-600">${data.pickup.address}</p>
                            <p class="text-xs text-gray-500">${data.pickup.person_name} - ${data.pickup.person_phone}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Delivery</h4>
                            <p class="text-sm text-gray-600">${data.delivery.address}</p>
                            <p class="text-xs text-gray-500">${data.delivery.receiver_name} - ${data.delivery.receiver_phone}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-xs text-gray-500">Status</p>
                            <p class="font-medium">${data.status}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Cost</p>
                            <p class="font-medium">CF$ ${data.estimated_cost}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Distance</p>
                            <p class="font-medium">${data.distance_km} km</p>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('bookingModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading booking details:', error);
        });
}

// Close booking modal
function closeBookingModal() {
    document.getElementById('bookingModal').classList.add('hidden');
}

// Refresh map data
function refreshMap() {
    loadMapData();
    // Refresh statistics
    location.reload();
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const deliveryItems = document.querySelectorAll('.delivery-item');
        
        deliveryItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Status filter
    document.getElementById('statusFilter').addEventListener('change', function(e) {
        const status = e.target.value;
        const url = new URL(window.location);
        url.searchParams.set('status', status);
        window.location.href = url.toString();
    });
    
    // Auto-refresh toggle
    document.getElementById('autoRefresh').addEventListener('change', function(e) {
        if (e.target.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });
}

// Auto-refresh functionality
function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        loadMapData();
    }, 30000); // Refresh every 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

// Close modal when clicking outside
document.getElementById('bookingModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBookingModal();
    }
});
</script>

<script>
function loadGoogleMapsScript() {
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&callback=initializeMap`;
    script.async = true;
    script.defer = true;
    script.onerror = () => {
        const mapDiv = document.getElementById('map');
        mapDiv.innerHTML = `
            <div class="text-center text-red-500">
                <i class="fas fa-exclamation-triangle text-4xl"></i>
                <p class="mt-4">Failed to load Google Maps.</p>
                <p class="text-sm text-gray-600">Please check your API key and network connection.</p>
            </div>
        `;
    };
    document.head.appendChild(script);
}
</script>
@endpush
