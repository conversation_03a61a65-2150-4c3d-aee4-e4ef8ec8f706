<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show the admin dashboard
     */
    public function index()
    {
        // Get key statistics
        $stats = [
            'total_bookings' => Booking::count(),
            'pending_bookings' => Booking::where('status', 'pending')->count(),
            'active_bookings' => Booking::whereIn('status', ['confirmed', 'in_progress'])->count(),
            'delivered_today' => Booking::where('status', 'delivered')
                ->whereDate('delivered_at', today())
                ->count(),
            'total_customers' => User::where('role', 'customer')->count(),

            'revenue_today' => $this->getRevenueForPeriod('today'),
            'revenue_week' => $this->getRevenueForPeriod('week'),
            'revenue_month' => $this->getRevenueForPeriod('month'),
        ];

        // Get recent bookings
        $recentBookings = Booking::with(['customer'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get pending bookings
        $pendingBookings = Booking::with('customer')
            ->where('status', 'pending')
            ->orderBy('created_at', 'asc')
            ->limit(5)
            ->get();

        // Get booking trends for chart (last 7 days)
        $bookingTrends = $this->getBookingTrends();

        // Get revenue trends for chart (last 30 days)
        $revenueTrends = $this->getRevenueTrends();

        return view('admin.dashboard', compact(
            'stats',
            'recentBookings',
            'pendingBookings',
            'bookingTrends',
            'revenueTrends'
        ));
    }



    /**
     * Get revenue for a specific period
     */
    private function getRevenueForPeriod(string $period): float
    {
        $query = Booking::where('status', 'delivered');

        switch ($period) {
            case 'today':
                $query->whereDate('delivered_at', today());
                break;
            case 'week':
                $query->whereBetween('delivered_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('delivered_at', now()->month)
                      ->whereYear('delivered_at', now()->year);
                break;
        }

        return $query->sum('final_cost') ?: $query->sum('estimated_cost');
    }

    /**
     * Get booking trends for the last 7 days
     */
    private function getBookingTrends(): array
    {
        $trends = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = Booking::whereDate('created_at', $date)->count();
            
            $trends[] = [
                'date' => $date->format('M j'),
                'count' => $count
            ];
        }

        return $trends;
    }

    /**
     * Get revenue trends for the last 30 days
     */
    private function getRevenueTrends(): array
    {
        $trends = [];
        
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Booking::where('status', 'delivered')
                ->whereDate('delivered_at', $date)
                ->sum(DB::raw('COALESCE(final_cost, estimated_cost)'));
            
            $trends[] = [
                'date' => $date->format('M j'),
                'revenue' => $revenue
            ];
        }

        return $trends;
    }



    /**
     * Show settings page
     */
    public function settings()
    {
        // This would typically load settings from a database table
        // For now, we'll use default values
        $settings = [
            'base_costs' => [
                'document' => 15.00,
                'small' => 20.00,
                'medium' => 35.00,
                'large' => 50.00,
            ],
            'cost_per_km' => 2.50,
            'cost_per_kg' => 2.50,
            'minimum_cost' => 15.00,
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * Update settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'base_costs.document' => 'required|numeric|min:0',
            'base_costs.small' => 'required|numeric|min:0',
            'base_costs.medium' => 'required|numeric|min:0',
            'base_costs.large' => 'required|numeric|min:0',
            'cost_per_km' => 'required|numeric|min:0',
            'cost_per_kg' => 'required|numeric|min:0',
            'minimum_cost' => 'required|numeric|min:0',
        ]);

        // In a real application, you would save these to a settings table
        // For now, we'll just return success
        
        return back()->with('success', 'Settings updated successfully!');
    }
}
