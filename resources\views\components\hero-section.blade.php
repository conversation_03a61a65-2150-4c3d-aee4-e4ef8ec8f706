@props(['title', 'subtitle', 'primaryButtonText' => 'Get Started', 'primaryButtonUrl' => '#', 'secondaryButtonText' => 'Learn More', 'secondaryButtonUrl' => '#'])

<section class="hero-section relative text-white min-h-screen flex items-center">
    <div class="absolute inset-0 bg-black/85"></div>
    <div class="container mx-auto px-6 relative z-10">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div class="text-content">
                <h1 class="text-5xl md:text-7xl font-extrabold leading-tight">{{ $title }}</h1>
                <p class="mt-6 text-lg text-gray-300 max-w-xl">{{ $subtitle }}</p>
                <div class="mt-10 flex flex-col sm:flex-row gap-4">
                    <a href="{{ $primaryButtonUrl }}" class="bg-orange-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-700 transition-colors text-center">
                        {{ $primaryButtonText }}
                    </a>
                    <a href="{{ $secondaryButtonUrl }}" class="bg-white/10 border border-white/20 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/20 transition-colors text-center">
                        {{ $secondaryButtonText }}
                    </a>
                </div>
            </div>
            
            @if(isset($form))
                <div class="booking-form">
                    {{ $form }}
                </div>
            @endif
        </div>
    </div>
</section>
