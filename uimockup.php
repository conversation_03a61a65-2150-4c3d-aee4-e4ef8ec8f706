<?php
// List of User-Agent strings to block
$blocked_agents = array('curl', 'wget', 'python-requests');

// Get the User-Agent from the current request
$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? strtolower($_SERVER['HTTP_USER_AGENT']) : '';

// Check if the User-Agent contains any of the blocked strings
foreach ($blocked_agents as $agent) {
    if (strpos($user_agent, $agent) !== false) {
        // If a blocked agent is found, send a "Forbidden" header and stop execution
        header('HTTP/1.0 403 Forbidden');
        die('Access denied.');
    }
}

// If the script continues, the User-Agent is not in the blocklist.
// Your regular page content and HTML would follow here.
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTAJet Courier Service</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .brand-orange {
            background-color: #F97316;
        }
        .brand-orange-text {
            color: #F97316;
        }
        .brand-orange-border {
            border-color: #F97316;
        }
        .form-input-icon {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            pointer-events: none;
        }
        .form-input {
            padding-left: 3.5rem !important;
        }
        .screen {
            display: none;
        }
        #landing-page {
             min-height: 100vh;
        }
        .about-text {
            margin-top: 23vh;
        }

    </style>
</head>
<body class="bg-white text-gray-800">
    
    <noscript>
        <div style="display:flex; justify-content:center; align-items:center; height:100vh; font-family: sans-serif; font-size: 2rem; color: #333;">
            JavaScript is required to view this page.
        </div>
    </noscript>
    
<div id="root-content">

    <div id="app">

        <div id="landing-page" class="screen">
            <header class="bg-black text-white sticky top-0 z-40">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div class="text-2xl font-bold cursor-pointer back-to-home">
                        TTAJET.
                        <span class="block text-xs font-normal text-gray-300">Courier Service</span>
                    </div>
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#about" class="hover:text-orange-500 transition-colors">About</a>
                        <a href="#services" class="hover:text-orange-500 transition-colors">Services</a>
                        <button id="nav-to-booking" class="hover:text-orange-500 transition-colors">Book a Delivery</button>
                        <button id="nav-to-dashboard" class="bg-orange-500 px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">Admin Dashboard</button>
                    </div>
                    <div class="md:hidden">
                        <button class="text-white focus:outline-none">
                            <i class="fas fa-bars text-2xl"></i>
                        </button>
                    </div>
                </nav>
            </header>

            <div class="relative">
                <div class="bg-black text-white pt-16 pb-32 md:pb-48">
                    <div class="container mx-auto px-6">
                        <div class="max-w-xl text-content">
                             <h1 class="text-4xl md:text-6xl font-bold leading-tight">Fast and Reliable Courier Services</h1>
                             <p class="mt-4 text-gray-300">Book a courier with TTA Jet for quick and secure delivery in Accra.</p>
                        </div>
                    </div>
                </div>

                <div class="container mx-auto px-6 -mt-24 md:-mt-40 relative z-10">
                    <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-8 items-start">
                        <div class="booking-form bg-white p-8 rounded-xl shadow-2xl lg:col-span-2">
                            <h2 class="text-2xl font-bold mb-6">Quick Booking</h2>
                            <form id="quick-booking-form" class="space-y-4">
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt form-input-icon"></i>
                                    <input type="text" placeholder="Enter pickup location" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt form-input-icon"></i>
                                    <input type="text" placeholder="Enter delivery location" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-box form-input-icon"></i>
                                    <select class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input appearance-none">
                                        <option>Select package type</option>
                                        <option>Small Box</option>
                                        <option>Medium Box</option>
                                        <option>Large Box</option>
                                        <option>Document</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                                </div>
                                <button type="button" id="get-started-btn" class="w-full brand-orange text-white font-bold py-3 rounded-lg hover:bg-orange-600 transition-transform transform hover:scale-105">
                                    Get Started
                                </button>
                            </form>
                        </div>
                        <div id="about" class="about-text pt-8 lg:col-span-3" >
                             <h2 class="text-3xl font-bold">About TTA Jet</h2>
                             <p class="mt-4 text-gray-600">TTA Jet is your trusted partner for fast and reliable courier delivery within Accra. We're committed to providing exceptional service with every package we handle.</p>
                        </div>
                    </div>
                    
                </div>
            </div>

            <section id="services" class="bg-white py-16 md:py-24">
                <div class="container mx-auto px-6">
                     <h2 class="text-3xl font-bold mb-8 text-center">Our Services</h2>
                     <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div class="service-card text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-xl transition-shadow">
                            <i class="fas fa-shipping-fast text-4xl brand-orange-text"></i>
                            <h3 class="font-bold mt-4">Same-Day Delivery</h3>
                            <p class="text-sm text-gray-600 mt-2">Get your packages delivered on same day.</p>
                        </div>
                        <div class="service-card text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-xl transition-shadow">
                            <i class="fas fa-box-open text-4xl brand-orange-text"></i>
                            <h3 class="font-bold mt-4">Small Packages</h3>
                            <p class="text-sm text-gray-600 mt-2">We handle small parcels with care and efficiency.</p>
                        </div>
                        <div class="service-card text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-xl transition-shadow">
                            <i class="fas fa-store text-4xl brand-orange-text"></i>
                            <h3 class="font-bold mt-4">Business Solutions</h3>
                            <p class="text-sm text-gray-600 mt-2">Courier services tailored for small businesses.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <div id="booking-page" class="screen bg-gray-100 flex items-center justify-center py-12 px-4 min-h-screen">
            <div class="max-w-4xl w-full bg-white p-8 md:p-12 rounded-2xl shadow-2xl">
                 <button class="back-to-home mb-6 text-gray-500 hover:text-orange-500"><i class="fas fa-arrow-left mr-2"></i>Back to Home</button>
                <h1 class="text-3xl md:text-4xl font-bold text-center mb-8">Book a Delivery</h1>
                <form id="detailed-booking-form">
                    <div class="grid md:grid-cols-2 gap-x-8 gap-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Location</label>
                            <div class="relative">
                                <i class="fas fa-map-marker-alt form-input-icon"></i>
                                <input type="text" placeholder="Enter pickup address" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Delivery Location</label>
                            <div class="relative">
                                <i class="fas fa-map-marker-alt form-input-icon"></i>
                                <input type="text" placeholder="Enter delivery address" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Person's Name</label>
                            <div class="relative">
                                <i class="fas fa-user form-input-icon"></i>
                                <input type="text" placeholder="Enter name" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Person's Contact</label>
                            <div class="relative">
                                <i class="fas fa-phone form-input-icon"></i>
                                <input type="tel" placeholder="Enter phone number" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Receiver's Name</label>
                            <div class="relative">
                                <i class="fas fa-user-check form-input-icon"></i>
                                <input type="text" placeholder="Enter name" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Receiver's Phone</label>
                            <div class="relative">
                                <i class="fas fa-phone-alt form-input-icon"></i>
                                <input type="tel" placeholder="Enter phone number" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Package Type</label>
                             <div class="relative">
                                <i class="fas fa-box form-input-icon"></i>
                                <select id="package-type" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none form-input">
                                    <option value="small">Small Box</option>
                                    <option value="medium">Medium Box</option>
                                    <option value="large">Large Box</option>
                                    <option value="document">Document</option>
                                </select>
                                <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Time</label>
                            <div class="relative">
                                <i class="fas fa-clock form-input-icon"></i>
                                <select class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none form-input">
                                    <option>Now</option>
                                    <option>In 1 hour</option>
                                    <option>In 2 hours</option>
                                </select>
                                <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Package Weight (kg)</label>
                            <div class="relative">
                                <i class="fas fa-weight-hanging form-input-icon"></i>
                                <input id="package-weight" type="number" placeholder="Enter weight" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                            <div class="flex items-center space-x-4 mt-2 h-full">
                                <label class="flex items-center"><input type="radio" name="payment" class="mr-2 text-orange-500 focus:ring-orange-500" checked> Mobile Money</label>
                                <label class="flex items-center"><input type="radio" name="payment" class="mr-2 text-orange-500 focus:ring-orange-500"> Card</label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
                        <p class="text-lg">Estimated Cost: <span id="estimated-cost" class="font-bold text-xl brand-orange-text">CF$ 25,00</span></p>
                        <button type="button" id="submit-booking-btn" class="w-full sm:w-auto brand-orange text-white font-bold py-3 px-12 rounded-lg hover:bg-orange-600 transition-transform transform hover:scale-105">
                            Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div id="dashboard-page" class="screen min-h-screen">
            <header class="bg-black text-white sticky top-0 z-50">
                <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div class="text-2xl font-bold cursor-pointer back-to-home">
                        TTAJET.
                        <span class="block text-xs font-normal text-gray-300">Courier Service</span>
                    </div>
                    <div class="flex items-center space-x-8">
                        <button id="nav-to-dashboard-from-dash" class="text-orange-500 font-semibold">Dashboard</button>
                        <button id="nav-to-booking-from-dash" class="hover:text-orange-500 transition-colors">Book a Delivery</button>
                        <button id="sign-out-btn" class="hover:text-orange-500 transition-colors">Sign Out</button>
                    </div>
                </nav>
            </header>

            <main class="container mx-auto px-6 py-12">
                <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="stat-card bg-white p-6 rounded-xl shadow-lg text-center">
                        <p class="text-gray-500">Total Bookings</p>
                        <p id="total-bookings" class="text-5xl font-bold mt-2">125</p>
                    </div>
                     <div class="stat-card bg-white p-6 rounded-xl shadow-lg text-center">
                        <p class="text-gray-500">In Transit</p>
                        <p id="in-transit" class="text-5xl font-bold mt-2">8</p>
                    </div>
                     <div class="stat-card brand-orange text-white p-6 rounded-xl shadow-lg text-center">
                    </div>
                </div>

                <div class="mt-12 bg-white p-8 rounded-xl shadow-lg">
                    <h2 class="text-2xl font-bold mb-6">Bookings</h2>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left min-w-[640px]">
                            <thead>
                                <tr class="border-b text-gray-500">
                                    <th class="py-3 pr-4">Booking ID</th>
                                    <th class="py-3 pr-4">Pickup Location</th>
                                    <th class="py-3 pr-4">Status</th>
                                    <th class="py-3 pr-4 text-right">Action</th>
                                </tr>
                            </thead>
                            <tbody id="bookings-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white p-8 rounded-lg shadow-xl text-center mx-4">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-check text-green-500 text-3xl"></i>
            </div>
            <h2 class="text-2xl font-bold mt-4">Booking Successful!</h2>
            <p class="text-gray-600 mt-2">Your delivery has been scheduled.</p>
            <button id="close-modal-btn" class="mt-6 bg-green-500 text-white font-bold py-2 px-8 rounded-lg hover:bg-green-600">
                OK
            </button>
        </div>
    </div>

</div>

<script>
        // Show the content if JavaScript is enabled
        document.getElementById('root-content').style.display = 'block';
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const screens = {
                landing: document.getElementById('landing-page'),
                booking: document.getElementById('booking-page'),
                dashboard: document.getElementById('dashboard-page'),
            };

            const modal = document.getElementById('success-modal');

            let bookingsData = [
                { id: '12345', location: '123 First St, Accra', status: 'Pending' },
                { id: '12344', location: '456 Market Rd, Accra', status: 'Pending' },
                { id: '12343', location: '789 Oak Ave, Accra', status: 'In Transit' },
                { id: '12342', location: '321 Elm St, Accra', status: 'Delivered' },
                { id: '12341', location: '555 Pine Ln, Accra', status: 'Pending' },
                { id: '12340', location: '888 Maple Dr, Accra', status: 'Delivered' },
            ];

            function renderDashboard() {
                const tableBody = document.getElementById('bookings-table-body');
                tableBody.innerHTML = ''; 

                let pendingCount = 0;
                let inTransitCount = 0;

                bookingsData.forEach(booking => {
                    if (booking.status === 'Pending') pendingCount++;
                    if (booking.status === 'In Transit') inTransitCount++;

                    const row = document.createElement('tr');
                    row.className = 'border-b last:border-b-0';

                    let statusClass = '';
                    let actionButton = '';

                    switch (booking.status) {
                        case 'Pending':
                            statusClass = 'text-yellow-500';
                            actionButton = `<button data-id="${booking.id}" class="bg-gray-200 text-gray-700 px-4 py-1 rounded-md text-sm hover:bg-gray-300">Details</button>`;
                            break;
                        case 'In Transit':
                            statusClass = 'text-blue-500';
                            actionButton = `<button disabled class="bg-blue-100 text-blue-600 px-4 py-1 rounded-md text-sm cursor-not-allowed">In Transit</button>`;
                            break;
                        case 'Delivered':
                            statusClass = 'text-green-500';
                             actionButton = `<button disabled class="bg-green-100 text-green-600 px-4 py-1 rounded-md text-sm cursor-not-allowed">Delivered</button>`;
                            break;
                    }

                    row.innerHTML = `
                        <td class="py-4 pr-4 font-medium">${booking.id}</td>
                        <td class="py-4 pr-4 text-gray-600">${booking.location}</td>
                        <td class="py-4 pr-4 font-semibold ${statusClass}">${booking.status}</td>
                        <td class="py-4 pr-4 text-right">${actionButton}</td>
                    `;
                    tableBody.appendChild(row);
                });

                document.getElementById('total-bookings').textContent = bookingsData.length;
                document.getElementById('in-transit').textContent = inTransitCount;
            }

            function showScreen(screenKey) {
                const targetScreen = screens[screenKey];
                if (!targetScreen) return;
                
                window.scrollTo(0, 0);

                Object.values(screens).forEach(screen => {
                    if (screen !== targetScreen) {
                        gsap.to(screen, { autoAlpha: 0, duration: 0, onComplete: () => screen.style.display = 'none' });
                    }
                });

                targetScreen.style.display = 'block';
                if (screenKey === 'booking') {
                     targetScreen.style.display = 'flex';
                }
                gsap.fromTo(targetScreen, 
                    { autoAlpha: 0, y: 20 }, 
                    { autoAlpha: 1, y: 0, duration: 0.5, ease: 'power2.out' }
                );

                if (screenKey === 'dashboard') {
                    renderDashboard();
                    gsap.from(".stat-card", {
                        duration: 0.5,
                        y: 30,
                        opacity: 0,
                        stagger: 0.1,
                        ease: 'power2.out'
                    });
                }
            }

            document.getElementById('get-started-btn').addEventListener('click', () => showScreen('booking'));
            document.getElementById('nav-to-booking').addEventListener('click', () => showScreen('booking'));
            document.getElementById('nav-to-booking-from-dash').addEventListener('click', () => showScreen('booking'));
            document.getElementById('nav-to-dashboard').addEventListener('click', () => showScreen('dashboard'));
            document.getElementById('nav-to-dashboard-from-dash').addEventListener('click', () => showScreen('dashboard'));
            document.getElementById('sign-out-btn').addEventListener('click', () => showScreen('landing'));
            document.querySelectorAll('.back-to-home').forEach(btn => {
                btn.addEventListener('click', () => showScreen('landing'));
            });

            document.getElementById('submit-booking-btn').addEventListener('click', () => {
                const newBooking = {
                    id: Math.floor(10000 + Math.random() * 90000).toString(),
                    location: 'New Random St, Accra',
                    status: 'Pending'
                };
                bookingsData.unshift(newBooking);
                modal.classList.remove('hidden');
                gsap.fromTo(modal.firstElementChild, {scale: 0.7, opacity: 0}, {scale: 1, opacity: 1, duration: 0.3, ease: 'back.out(1.7)'});
            });
            
            document.getElementById('close-modal-btn').addEventListener('click', () => {
                 gsap.to(modal.firstElementChild, {scale: 0.7, opacity: 0, duration: 0.3, ease: 'back.in(1.7)', onComplete: () => {
                    modal.classList.add('hidden');
                    showScreen('dashboard');
                 }});
            });

            const packageTypeSelect = document.getElementById('package-type');
            const packageWeightInput = document.getElementById('package-weight');
            const costDisplay = document.getElementById('estimated-cost');

            function updateCost() {
                const type = packageTypeSelect.value;
                const weight = parseFloat(packageWeightInput.value) || 0;
                let baseCost = 20;
                if (type === 'medium') baseCost = 35;
                if (type === 'large') baseCost = 50;
                if (type === 'document') baseCost = 15;

                const finalCost = baseCost + (weight * 2.5);
                costDisplay.textContent = `CF$ ${finalCost.toFixed(2).replace('.', ',')}`;
            }
            packageTypeSelect.addEventListener('change', updateCost);
            packageWeightInput.addEventListener('input', updateCost);
            updateCost();

            document.getElementById('bookings-table-body').addEventListener('click', (e) => {
            });

            showScreen('landing');
            
            const tl = gsap.timeline({defaults: {ease: 'power3.out'}});
            tl.from('.text-content > *', {opacity: 0, y: 30, stagger: 0.2, duration: 0.8})
              .from('.booking-form', {opacity: 0, y: 30, duration: 0.6}, "-=0.5")
              .from('.about-text', {opacity: 0, y: 30, duration: 0.6}, "<")
              .from('.service-card', {opacity: 0, y: 20, stagger: 0.15, duration: 0.5}, "-=0.3");

        });
    </script>
    
    <script>
  document.addEventListener('contextmenu', e => e.preventDefault());
  document.onkeydown = function(e) {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) return false;
  };
</script>

<script>
    const clearContent = () => {
        // Clear the content and stop further execution
        document.body.innerHTML = '<div style="display:flex; justify-content:center; align-items:center; height:100vh; font-family: sans-serif; font-size: 2rem; color: #333;">Source code viewing is not permitted.</div>';
        throw new Error("Source code viewing is not permitted.");
    };

    document.addEventListener('keydown', function(e) {
        if (e.keyCode == 123 || (e.ctrlKey && e.shiftKey && (e.keyCode == 'I'.charCodeAt(0) || e.keyCode == 'C'.charCodeAt(0) || e.keyCode == 'J'.charCodeAt(0))) || (e.ctrlKey && e.keyCode == 'U'.charCodeAt(0))) {
            clearContent();
            e.preventDefault();
        }
    });

    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    const devtools = {
        open: false,
        orientation: null
    };
    const threshold = 170;

    const checkDevTools = () => {
        const widthThreshold = window.outerWidth - window.innerWidth > threshold;
        const heightThreshold = window.outerHeight - window.innerHeight > threshold;
        const orientation = widthThreshold ? 'vertical' : 'horizontal';

        if (widthThreshold || heightThreshold) {
            if (!devtools.open || devtools.orientation !== orientation) {
                clearContent();
            }
            devtools.open = true;
            devtools.orientation = orientation;
            
            debugger;

        } else {
            devtools.open = false;
            devtools.orientation = null;
        }
    };
    
    setInterval(checkDevTools, 500);
</script>


</body>
</html>
