# TTAJet Courier Service

A comprehensive Laravel-based courier service application with multi-role authentication, real-time tracking, and modern UI.

## 🚀 Quick Start

### Prerequisites
- PHP 8.2 or higher
- Composer
- MySQL 8.0 or higher
- XAMPP (for local development)

### Installation

1. **Navigate to the project directory:**
   ```bash
   cd C:\xampp\htdocs\ttajet
   ```

2. **Install PHP dependencies:**
   ```bash
   composer install
   ```

3. **Set up environment configuration:**
   ```bash
   copy .env.example .env
   ```

4. **Configure your database in `.env`:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=ttajet_courier
   DB_USERNAME=root
   DB_PASSWORD=
   ```

5. **Generate application key:**
   ```bash
   php artisan key:generate
   ```

6. **Create database:**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create a new database named `ttajet_courier`

7. **Run migrations and seed data:**
   ```bash
   php artisan migrate --seed
   ```

8. **Access the application:**
   - Visit: http://localhost
   - The application will automatically redirect to TTAJet

## 🎯 Features

### ✅ Implemented
- **Multi-role Authentication** (Customer, Admin, Rider)
- **Responsive Landing Page** with hero section and quick booking
- **Comprehensive Booking System** with real-time cost calculation
- **Admin Dashboard** with analytics and booking management
- **Customer Dashboard** with booking history and tracking
- **Google Maps Integration** (with fallback for development)
- **Database Schema** for all core features
- **Role-based Access Control**
- **Modern UI** with Tailwind CSS and GSAP animations

### 🔄 Ready for Implementation
- Rider interface and job management
- Real-time location tracking
- Payment gateway integration
- Email notifications
- Real-time notifications (Pusher/FCM)
- Rating and review system

## 👥 User Roles & Access

### Default Accounts (after seeding)

**Admin:**
- Email: <EMAIL>
- Password: password
- Access: Full system management

**Customer:**
- Email: <EMAIL>
- Password: password
- Access: Booking and tracking

**Riders:**
- Email: <EMAIL> / <EMAIL>
- Password: password
- Access: Job management (when implemented)

## 🗄️ Database Schema

### Core Tables
- **users** - Multi-role user management
- **bookings** - Comprehensive booking data
- **rider_locations** - Real-time location tracking
- **payments** - Payment processing
- **reviews** - Customer feedback
- **notifications** - User notifications

## 🛠️ Development

### Project Structure
```
ttajet/
├── app/
│   ├── Http/Controllers/     # Application controllers
│   ├── Models/              # Eloquent models
│   ├── Services/            # Business logic services
│   └── Providers/           # Service providers
├── database/
│   ├── migrations/          # Database migrations
│   └── seeders/            # Database seeders
├── resources/views/         # Blade templates
├── routes/                  # Application routes
└── public/                  # Public assets
```

### Key Services
- **CostCalculationService** - Dynamic pricing calculation
- **GoogleMapsService** - Address geocoding and distance calculation
- **RoleMiddleware** - Role-based access control

## 🎨 UI/UX Features

- **Responsive Design** - Mobile-first approach
- **Smooth Animations** - GSAP-powered transitions
- **Real-time Updates** - AJAX forms and live calculations
- **Toast Notifications** - User feedback system
- **Loading States** - Enhanced user experience

## 🔧 Configuration

### Google Maps (Optional)
Add your Google Maps API keys to `.env`:
```env
GOOGLE_MAPS_API_KEY=your_api_key_here
GOOGLE_MAPS_GEOCODING_API_KEY=your_geocoding_key
GOOGLE_MAPS_DIRECTIONS_API_KEY=your_directions_key
```

### Payment Gateways (Future)
```env
STRIPE_KEY=your_stripe_key
STRIPE_SECRET=your_stripe_secret
PAYSTACK_PUBLIC_KEY=your_paystack_key
PAYSTACK_SECRET_KEY=your_paystack_secret
```

## 📱 Accessing the Application

1. **Main Application:** http://localhost
2. **UI Mockup (Demo):** http://localhost/ttajet/uimockup.php
3. **Admin Dashboard:** http://localhost/admin/dashboard (after login)
4. **Customer Dashboard:** http://localhost/customer/dashboard (after login)

## 🚨 Troubleshooting

### Common Issues

1. **"Vendor directory not found"**
   - Run `composer install` in the ttajet directory

2. **"Application key not set"**
   - Run `php artisan key:generate`

3. **Database connection errors**
   - Check your `.env` database configuration
   - Ensure MySQL is running in XAMPP

4. **Permission errors**
   - Ensure the `storage` and `bootstrap/cache` directories are writable

### Getting Help

If you encounter issues:
1. Check the Laravel logs in `storage/logs/`
2. Verify your `.env` configuration
3. Ensure all dependencies are installed
4. Check that your database is properly configured

## 📄 License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

---

**TTAJet Courier Service** - Fast and Reliable Delivery Solutions
