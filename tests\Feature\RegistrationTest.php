<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RegistrationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that registration page loads correctly.
     */
    public function test_registration_page_loads(): void
    {
        $response = $this->get('/register');
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
    }

    /**
     * Test successful customer registration.
     */
    public function test_customer_can_register_successfully(): void
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone_number' => '+233123456789',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'customer'
        ];

        $response = $this->post('/register', $userData);

        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone_number' => '+233123456789',
            'role' => 'customer',
            'is_approved' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Test registration fails without phone number.
     */
    public function test_registration_fails_without_phone_number(): void
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'customer'
            // Missing phone_number
        ];

        $response = $this->post('/register', $userData);

        $response->assertSessionHasErrors(['phone_number']);
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }

    /**
     * Test registration fails with invalid phone number.
     */
    public function test_registration_fails_with_invalid_phone_number(): void
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone_number' => '123', // Too short
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'customer'
        ];

        $response = $this->post('/register', $userData);

        $response->assertSessionHasErrors(['phone_number']);
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }

    /**
     * Test that only customer role is accepted.
     */
    public function test_only_customer_role_accepted(): void
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone_number' => '+233123456789',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'admin' // Should fail
        ];

        $response = $this->post('/register', $userData);

        $response->assertSessionHasErrors(['role']);
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }
}
