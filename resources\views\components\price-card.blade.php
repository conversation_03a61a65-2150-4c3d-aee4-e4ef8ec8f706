@props(['icon', 'title', 'weight', 'price', 'popular' => false, 'bookingUrl' => '#'])

<div class="price-card text-center p-8 rounded-2xl {{ $popular ? 'bg-white text-gray-900 shadow-2xl relative scale-105' : 'glass-card text-white shadow-lg' }}">
    @if($popular)
        <div class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-orange-600 text-white font-bold text-xs px-3 py-1 rounded-full uppercase tracking-wider">Most Popular</div>
    @endif
    
    <i class="{{ $icon }} fa-3x mb-6 {{ $popular ? 'text-orange-600' : 'text-white opacity-70' }}"></i>
    <h3 class="text-2xl font-bold">{{ $title }}</h3>
    <p class="{{ $popular ? 'text-gray-500' : 'opacity-70' }} text-sm mt-1">{{ $weight }}</p>
    <p class="text-5xl font-extrabold my-6 {{ $popular ? 'text-orange-600' : '' }}">{{ $price }}</p>
    
    <a href="{{ $bookingUrl }}" class="{{ $popular ? 'bg-orange-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-orange-700' : 'bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg' }} transition-colors w-full block">
        Book Now
    </a>
</div>
