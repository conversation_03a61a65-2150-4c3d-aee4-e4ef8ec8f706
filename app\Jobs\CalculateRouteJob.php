<?php

namespace App\Jobs;

use App\Models\Booking;
use App\Models\RiderLocation;
use App\Services\RouteOptimizationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CalculateRouteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $booking;
    /**
     * Create a new job instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
        
        $this->timeout = 120; // 2 minutes
    }

    /**
     * Execute the job.
     */
    public function handle(RouteOptimizationService $routeService): void
    {
        try {
            Log::info("Calculating route for booking {$this->booking->booking_id}");

            // Calculate route
            $routeData = $routeService->calculateRoute($this->booking);

            // Update booking with route data
            $this->updateBookingWithRouteData($routeData);

            // Cache route data for live map
            $this->cacheRouteData($routeData);

            // Update API call statistics
            $this->updateApiStatistics();

            Log::info("Route calculated successfully for booking {$this->booking->booking_id}");

        } catch (\Exception $e) {
            Log::error("Route calculation failed for booking {$this->booking->booking_id}: " . $e->getMessage());
            $this->handleCalculationFailure($e);
        }
    }

    /**
     * Update booking with calculated route data
     */
    protected function updateBookingWithRouteData(array $routeData): void
    {
        $updateData = [];

        // Update distance and duration
        if (isset($routeData['total_distance_km'])) {
            $updateData['distance_km'] = $routeData['total_distance_km'];
        }

        if (isset($routeData['duration_in_traffic_minutes'])) {
            $updateData['estimated_duration_minutes'] = $routeData['duration_in_traffic_minutes'];
        } elseif (isset($routeData['total_duration_minutes'])) {
            $updateData['estimated_duration_minutes'] = $routeData['total_duration_minutes'];
        }

        // Update cost if distance changed significantly
        if (isset($updateData['distance_km']) && !$this->booking->final_cost) {
            $newCost = $this->calculateCostFromDistance($updateData['distance_km']);
            if (abs($newCost - $this->booking->estimated_cost) > 5) { // More than $5 difference
                $updateData['estimated_cost'] = $newCost;
            }
        }

        if (!empty($updateData)) {
            $this->booking->update($updateData);
        }
    }

    /**
     * Calculate cost based on distance
     */
    protected function calculateCostFromDistance(float $distance): float
    {
        // Base cost calculation (this should match your pricing logic)
        $baseCost = 10.00; // Base cost in dollars
        $costPerKm = 2.50; // Cost per kilometer
        
        return $baseCost + ($distance * $costPerKm);
    }

    /**
     * Cache route data for live map and tracking
     */
    protected function cacheRouteData(array $routeData): void
    {
        $cacheKey = "route_data_booking_{$this->booking->id}";
        
        $cacheData = [
            'booking_id' => $this->booking->id,
            'booking_reference' => $this->booking->booking_id,
            'route_data' => $routeData,
            'calculated_at' => now()->toISOString(),
        ];

        // Cache for 30 minutes
        Cache::put($cacheKey, $cacheData, 1800);

        // Also cache for live map
        $liveMapKey = "live_map_route_{$this->booking->id}";
        Cache::put($liveMapKey, [
            'polyline' => $routeData['polyline'] ?? null,
            'legs' => $routeData['legs'] ?? [],
            'total_distance' => $routeData['total_distance_km'] ?? 0,
            'total_duration' => $routeData['duration_in_traffic_minutes'] ?? $routeData['total_duration_minutes'] ?? 0,
            'eta_to_delivery' => $routeData['eta_to_delivery'] ?? null,
        ], 1800);
    }

    /**
     * Update API call statistics
     */
    protected function updateApiStatistics(): void
    {
        $today = now()->format('Y-m-d');
        
        // Update daily API call count
        $apiCallsKey = "google_maps_calls_{$today}";
        $currentCalls = Cache::get($apiCallsKey, 0);
        Cache::put($apiCallsKey, $currentCalls + 1, 86400); // 24 hours

        // Update total routes calculated
        $routesKey = "routes_calculated_count";
        $totalRoutes = Cache::get($routesKey, 0);
        Cache::put($routesKey, $totalRoutes + 1, 86400);

        // Track calculation time (simplified)
        $avgTimeKey = "average_calculation_time";
        $currentAvg = Cache::get($avgTimeKey, 0);
        $newAvg = ($currentAvg + 2) / 2; // Simplified average
        Cache::put($avgTimeKey, $newAvg, 86400);
    }

    /**
     * Handle calculation failure
     */
    protected function handleCalculationFailure(\Exception $e): void
    {
        // Log the error
        Log::error("Route calculation failed for booking {$this->booking->booking_id}: " . $e->getMessage());

        // Use fallback calculation
        $fallbackDistance = $this->calculateStraightLineDistance();
        $fallbackDuration = max(15, $fallbackDistance * 3); // 3 minutes per km minimum

        $this->booking->update([
            'distance_km' => $fallbackDistance,
            'estimated_duration_minutes' => $fallbackDuration,
        ]);

        // Create notification for admins about API failure
        $errorNotification = [
            'role' => 'admin',
            'title' => 'Route Calculation Failed',
            'message' => "Route calculation failed for booking {$this->booking->booking_id}. Using fallback calculation.",
            'type' => 'system',
            'data' => [
                'booking_id' => $this->booking->id,
                'booking_reference' => $this->booking->booking_id,
                'error' => $e->getMessage(),
                'fallback_distance' => $fallbackDistance,
                'fallback_duration' => $fallbackDuration,
            ],
        ];

        SendNotificationJob::dispatch($errorNotification);
    }

    /**
     * Calculate straight-line distance as fallback
     */
    protected function calculateStraightLineDistance(): float
    {
        $lat1 = $this->booking->pickup_latitude;
        $lon1 = $this->booking->pickup_longitude;
        $lat2 = $this->booking->delivery_latitude;
        $lon2 = $this->booking->delivery_longitude;

        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("CalculateRouteJob failed for booking {$this->booking->booking_id}: " . $exception->getMessage());
        
        // Use emergency fallback
        $this->handleCalculationFailure($exception);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'booking:' . $this->booking->id,
            'route-calculation',
            'automation'
        ];
    }
}
