{"name": "carbonphp/carbon-doctrine-types", "description": "Types to use Carbon in Doctrine", "type": "library", "keywords": ["date", "time", "DateTime", "Carbon", "Doctrine"], "require": {"php": "^8.1"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "license": "MIT", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "minimum-stability": "dev"}