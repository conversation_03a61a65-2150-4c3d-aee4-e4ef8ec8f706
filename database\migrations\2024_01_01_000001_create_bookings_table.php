<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('booking_id')->unique(); // Public booking reference
            
            // Pickup Information
            $table->string('pickup_address');
            $table->decimal('pickup_latitude', 10, 8)->nullable();
            $table->decimal('pickup_longitude', 11, 8)->nullable();
            $table->string('pickup_person_name');
            $table->string('pickup_person_phone');
            
            // Delivery Information
            $table->string('delivery_address');
            $table->decimal('delivery_latitude', 10, 8)->nullable();
            $table->decimal('delivery_longitude', 11, 8)->nullable();
            $table->string('receiver_name');
            $table->string('receiver_phone');
            
            // Package Information
            $table->enum('package_type', ['small', 'medium', 'large', 'document']);
            $table->decimal('package_weight', 8, 2)->nullable();
            $table->text('package_description')->nullable();
            
            // Pricing and Payment
            $table->decimal('estimated_cost', 10, 2);
            $table->decimal('final_cost', 10, 2)->nullable();
            $table->enum('payment_method', ['mobile_money', 'card', 'cash']);
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            
            // Timing
            $table->enum('pickup_time_preference', ['now', '1_hour', '2_hours', 'scheduled']);
            $table->timestamp('scheduled_pickup_time')->nullable();
            $table->timestamp('actual_pickup_time')->nullable();
            $table->timestamp('delivered_at')->nullable();
            
            // Status and Distance
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'delivered', 'cancelled'])->default('pending');
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->integer('estimated_duration_minutes')->nullable();
            
            // Additional Information
            $table->text('special_instructions')->nullable();
            $table->text('cancellation_reason')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index('booking_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
