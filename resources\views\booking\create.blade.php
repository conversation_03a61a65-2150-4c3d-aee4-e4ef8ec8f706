@extends('layouts.app')

@section('title', 'Book a Delivery - TTAJet')

@section('content')
<div class="bg-gray-100 min-h-screen py-12 px-4">
    <div class="max-w-4xl mx-auto">
        
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('home') }}" class="text-gray-500 hover:text-orange-500 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Home
            </a>
        </div>
        
        <!-- Main Form Container -->
        <div class="bg-white p-8 md:p-12 rounded-2xl shadow-2xl">
            <h1 class="text-3xl md:text-4xl font-bold text-center mb-8">Book a Delivery</h1>
            
            <form id="booking-form" method="POST" action="{{ route('booking.store') }}">
                @csrf
                
                <div class="grid md:grid-cols-2 gap-x-8 gap-y-6">
                    
                    <!-- Pickup Location -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Location</label>
                        <div class="relative">
                            <i class="fas fa-map-marker-alt form-input-icon"></i>
                            <input type="text" name="pickup_address" id="pickup_address" 
                                   value="{{ old('pickup_address', request('pickup_address')) }}"
                                   placeholder="Enter pickup address" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('pickup_address') border-red-500 @enderror">
                        </div>
                        @error('pickup_address')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Delivery Location -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Delivery Location</label>
                        <div class="relative">
                            <i class="fas fa-map-marker-alt form-input-icon"></i>
                            <input type="text" name="delivery_address" id="delivery_address" 
                                   value="{{ old('delivery_address', request('delivery_address')) }}"
                                   placeholder="Enter delivery address" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('delivery_address') border-red-500 @enderror">
                        </div>
                        @error('delivery_address')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Pickup Person's Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Person's Name</label>
                        <div class="relative">
                            <i class="fas fa-user form-input-icon"></i>
                            <input type="text" name="pickup_person_name" 
                                   value="{{ old('pickup_person_name') }}"
                                   placeholder="Enter name" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('pickup_person_name') border-red-500 @enderror">
                        </div>
                        @error('pickup_person_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Pickup Person's Contact -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Person's Contact</label>
                        <div class="relative">
                            <i class="fas fa-phone form-input-icon"></i>
                            <input type="tel" name="pickup_person_phone" 
                                   value="{{ old('pickup_person_phone') }}"
                                   placeholder="Enter phone number" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('pickup_person_phone') border-red-500 @enderror">
                        </div>
                        @error('pickup_person_phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Receiver's Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Receiver's Name</label>
                        <div class="relative">
                            <i class="fas fa-user-check form-input-icon"></i>
                            <input type="text" name="receiver_name" 
                                   value="{{ old('receiver_name') }}"
                                   placeholder="Enter name" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('receiver_name') border-red-500 @enderror">
                        </div>
                        @error('receiver_name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Receiver's Phone -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Receiver's Phone</label>
                        <div class="relative">
                            <i class="fas fa-phone-alt form-input-icon"></i>
                            <input type="tel" name="receiver_phone" 
                                   value="{{ old('receiver_phone') }}"
                                   placeholder="Enter phone number" 
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('receiver_phone') border-red-500 @enderror">
                        </div>
                        @error('receiver_phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Package Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Package Type</label>
                        <div class="relative">
                            <i class="fas fa-box form-input-icon"></i>
                            <select name="package_type" id="package_type" 
                                    class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none form-input @error('package_type') border-red-500 @enderror">
                                <option value="">Select package type</option>
                                <option value="small" {{ old('package_type', request('package_type')) == 'small' ? 'selected' : '' }}>Small Box</option>
                                <option value="medium" {{ old('package_type', request('package_type')) == 'medium' ? 'selected' : '' }}>Medium Box</option>
                                <option value="large" {{ old('package_type', request('package_type')) == 'large' ? 'selected' : '' }}>Large Box</option>
                                <option value="document" {{ old('package_type', request('package_type')) == 'document' ? 'selected' : '' }}>Document</option>
                            </select>
                            <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                        @error('package_type')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Pickup Time -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pickup Time</label>
                        <div class="relative">
                            <i class="fas fa-clock form-input-icon"></i>
                            <select name="pickup_time_preference" 
                                    class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none form-input @error('pickup_time_preference') border-red-500 @enderror">
                                <option value="now" {{ old('pickup_time_preference') == 'now' ? 'selected' : '' }}>Now</option>
                                <option value="1_hour" {{ old('pickup_time_preference') == '1_hour' ? 'selected' : '' }}>In 1 hour</option>
                                <option value="2_hours" {{ old('pickup_time_preference') == '2_hours' ? 'selected' : '' }}>In 2 hours</option>
                            </select>
                            <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                        @error('pickup_time_preference')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Package Weight -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Package Weight (kg)</label>
                        <div class="relative">
                            <i class="fas fa-weight-hanging form-input-icon"></i>
                            <input type="number" name="package_weight" id="package_weight" 
                                   value="{{ old('package_weight') }}"
                                   placeholder="Enter weight" step="0.1" min="0" max="100"
                                   class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 form-input @error('package_weight') border-red-500 @enderror">
                        </div>
                        @error('package_weight')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                        <div class="flex items-center space-x-4 mt-2 h-full">
                            <label class="flex items-center">
                                <input type="radio" name="payment_method" value="mobile_money" 
                                       {{ old('payment_method', 'mobile_money') == 'mobile_money' ? 'checked' : '' }}
                                       class="mr-2 text-orange-500 focus:ring-orange-500"> 
                                Mobile Money
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="payment_method" value="card" 
                                       {{ old('payment_method') == 'card' ? 'checked' : '' }}
                                       class="mr-2 text-orange-500 focus:ring-orange-500"> 
                                Card
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="payment_method" value="cash" 
                                       {{ old('payment_method') == 'cash' ? 'checked' : '' }}
                                       class="mr-2 text-orange-500 focus:ring-orange-500"> 
                                Cash
                            </label>
                        </div>
                        @error('payment_method')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <!-- Special Instructions -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Special Instructions (Optional)</label>
                    <textarea name="special_instructions" rows="3" 
                              placeholder="Any special handling instructions..."
                              class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 @error('special_instructions') border-red-500 @enderror">{{ old('special_instructions') }}</textarea>
                    @error('special_instructions')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Cost and Submit -->
                <div class="mt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
                    <div class="text-lg">
                        Estimated Cost: 
                        <span id="estimated-cost" class="font-bold text-xl brand-orange-text">CF$ 25.00</span>
                        <div id="cost-loading" class="hidden">
                            <div class="spinner inline-block"></div>
                            <span class="ml-2 text-sm text-gray-500">Calculating...</span>
                        </div>
                    </div>
                    <button type="submit" id="submit-btn" 
                            class="w-full sm:w-auto brand-orange text-white font-bold py-3 px-12 rounded-lg hover:bg-orange-600 transition-transform transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="submit-text">Submit Booking</span>
                        <div id="submit-loading" class="hidden">
                            <div class="spinner inline-block"></div>
                            <span class="ml-2">Processing...</span>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white p-8 rounded-lg shadow-xl text-center mx-4 max-w-md">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <i class="fas fa-check text-green-500 text-3xl"></i>
        </div>
        <h2 class="text-2xl font-bold mt-4">Booking Successful!</h2>
        <p class="text-gray-600 mt-2">Your delivery has been scheduled.</p>
        <p class="text-sm text-gray-500 mt-1">Booking ID: <span id="booking-id" class="font-mono font-bold"></span></p>
        <div class="mt-6 space-y-2">
            <button id="view-booking-btn" class="w-full bg-orange-500 text-white font-bold py-2 px-8 rounded-lg hover:bg-orange-600">
                View Booking
            </button>
            <button id="close-modal-btn" class="w-full border border-gray-300 text-gray-700 font-bold py-2 px-8 rounded-lg hover:bg-gray-50">
                Close
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('booking-form');
    const packageTypeSelect = document.getElementById('package_type');
    const packageWeightInput = document.getElementById('package_weight');
    const pickupAddressInput = document.getElementById('pickup_address');
    const deliveryAddressInput = document.getElementById('delivery_address');
    const costDisplay = document.getElementById('estimated-cost');
    const costLoading = document.getElementById('cost-loading');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');
    const modal = document.getElementById('success-modal');
    
    let costCalculationTimeout;
    
    // Auto-calculate cost when inputs change
    function triggerCostCalculation() {
        clearTimeout(costCalculationTimeout);
        costCalculationTimeout = setTimeout(calculateCost, 500);
    }
    
    packageTypeSelect.addEventListener('change', triggerCostCalculation);
    packageWeightInput.addEventListener('input', triggerCostCalculation);
    pickupAddressInput.addEventListener('input', triggerCostCalculation);
    deliveryAddressInput.addEventListener('input', triggerCostCalculation);
    
    // Calculate cost function
    async function calculateCost() {
        const pickupAddress = pickupAddressInput.value.trim();
        const deliveryAddress = deliveryAddressInput.value.trim();
        const packageType = packageTypeSelect.value;
        const packageWeight = packageWeightInput.value || 0;
        
        if (!pickupAddress || !deliveryAddress || !packageType) {
            return;
        }
        
        costDisplay.style.display = 'none';
        costLoading.style.display = 'block';
        
        try {
            const response = await fetch('{{ route("api.bookings.calculate-cost") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    pickup_address: pickupAddress,
                    delivery_address: deliveryAddress,
                    package_type: packageType,
                    package_weight: packageWeight
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                costDisplay.textContent = data.formatted_cost;
            } else {
                costDisplay.textContent = 'Unable to calculate';
            }
        } catch (error) {
            console.error('Cost calculation error:', error);
            costDisplay.textContent = 'CF$ 25.00'; // Fallback
        } finally {
            costLoading.style.display = 'none';
            costDisplay.style.display = 'inline';
        }
    }
    
    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        submitBtn.disabled = true;
        submitText.style.display = 'none';
        submitLoading.style.display = 'inline-block';
        
        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('booking-id').textContent = data.booking_id;
                document.getElementById('view-booking-btn').onclick = function() {
                    window.location.href = '{{ route("customer.dashboard") }}';
                };
                modal.classList.remove('hidden');
                gsap.fromTo(modal.firstElementChild, 
                    {scale: 0.7, opacity: 0}, 
                    {scale: 1, opacity: 1, duration: 0.3, ease: 'back.out(1.7)'}
                );
            } else {
                showToast(data.message || 'Failed to create booking', 'error');
            }
        } catch (error) {
            console.error('Booking error:', error);
            showToast('An error occurred. Please try again.', 'error');
        } finally {
            submitBtn.disabled = false;
            submitText.style.display = 'inline';
            submitLoading.style.display = 'none';
        }
    });
    
    // Modal close handlers
    document.getElementById('close-modal-btn').addEventListener('click', function() {
        gsap.to(modal.firstElementChild, {
            scale: 0.7, 
            opacity: 0, 
            duration: 0.3, 
            ease: 'back.in(1.7)', 
            onComplete: () => {
                modal.classList.add('hidden');
                window.location.href = '{{ route("customer.dashboard") }}';
            }
        });
    });
    
    // Initial cost calculation if form is pre-filled
    if (packageTypeSelect.value && pickupAddressInput.value && deliveryAddressInput.value) {
        calculateCost();
    }
});
</script>
@endpush
