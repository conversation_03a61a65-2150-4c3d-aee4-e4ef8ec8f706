<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any existing users without phone numbers to have a placeholder
        // This ensures data integrity when we make the field required
        DB::table('users')
            ->whereNull('phone_number')
            ->orWhere('phone_number', '')
            ->update(['phone_number' => 'N/A']);

        // Now modify the column to be NOT NULL
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone_number')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the phone_number field back to nullable
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone_number')->nullable()->change();
        });
    }
};
