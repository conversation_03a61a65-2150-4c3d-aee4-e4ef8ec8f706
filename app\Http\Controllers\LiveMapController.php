<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use Illuminate\Http\Request;

class LiveMapController extends Controller
{
    /**
     * Show the live map deliveries view
     */
    public function index(Request $request)
    {
        $query = Booking::with(['customer']);

        // Filter by status - show only active deliveries by default
        $status = $request->get('status', 'active');
        if ($status === 'active') {
            $query->whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute']);
        } elseif ($status !== 'all') {
            $query->where('status', $status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('booking_id', 'like', "%{$search}%")
                  ->orWhere('pickup_address', 'like', "%{$search}%")
                  ->orWhere('delivery_address', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone_number', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Get statistics
        $stats = [
            'total_active' => Booking::whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute'])->count(),
            'pending_assignment' => Booking::where('status', 'pending')->count(),
            'in_transit' => Booking::whereIn('status', ['in_progress', 'delivery_enroute'])->count(),
            'completed_today' => Booking::where('status', 'completed')
                                      ->whereDate('updated_at', today())
                                      ->count()
        ];

        return view('live-map.index', compact('bookings', 'stats'));
    }

    /**
     * Get live tracking data for a specific booking
     */
    public function getBookingTrackingData(Booking $booking)
    {
        $booking->load(['customer']);

        // Simulate vehicle location for demonstration
        $vehicleLocation = $this->getSimulatedVehicleLocation($booking);

        return response()->json([
            'booking_id' => $booking->booking_id,
            'status' => $booking->status,
            'pickup' => [
                'address' => $booking->pickup_address,
                'latitude' => (float) $booking->pickup_latitude,
                'longitude' => (float) $booking->pickup_longitude,
                'person_name' => $booking->pickup_person_name,
                'person_phone' => $booking->pickup_person_phone
            ],
            'delivery' => [
                'address' => $booking->delivery_address,
                'latitude' => (float) $booking->delivery_latitude,
                'longitude' => (float) $booking->delivery_longitude,
                'receiver_name' => $booking->receiver_name,
                'receiver_phone' => $booking->receiver_phone
            ],
            'vehicle_location' => $vehicleLocation,
            'estimated_cost' => $booking->estimated_cost,
            'distance_km' => $booking->distance_km,
            'created_at' => $booking->created_at->format('Y-m-d H:i:s'),
            'actual_pickup_time' => $booking->actual_pickup_time?->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get all active deliveries for map display
     */
    public function getActiveDeliveries()
    {
        $bookings = Booking::with(['customer'])
        ->whereIn('status', ['confirmed', 'in_progress', 'delivery_enroute'])
        ->get();

        $deliveries = $bookings->map(function($booking) {
            return [
                'id' => $booking->id,
                'booking_id' => $booking->booking_id,
                'status' => $booking->status,
                'pickup' => [
                    'latitude' => (float) $booking->pickup_latitude,
                    'longitude' => (float) $booking->pickup_longitude,
                    'address' => $booking->pickup_address
                ],
                'delivery' => [
                    'latitude' => (float) $booking->delivery_latitude,
                    'longitude' => (float) $booking->delivery_longitude,
                    'address' => $booking->delivery_address
                ],
            ];
        });

        return response()->json($deliveries);
    }

    /**
     * Simulate vehicle location based on booking status and time
     */
    private function getSimulatedVehicleLocation(Booking $booking)
    {
        if ($booking->status !== 'in_progress' && $booking->status !== 'delivery_enroute') {
            return null;
        }

        $pickupLat = (float) $booking->pickup_latitude;
        $pickupLng = (float) $booking->pickup_longitude;
        $deliveryLat = (float) $booking->delivery_latitude;
        $deliveryLng = (float) $booking->delivery_longitude;

        // Simulate progress based on time since pickup
        $startTime = $booking->actual_pickup_time ?? $booking->created_at;
        $elapsedSeconds = now()->diffInSeconds($startTime);
        $totalDurationSeconds = $booking->estimated_duration_seconds ?? 3600; // Default to 1 hour

        $progress = min(1, $elapsedSeconds / $totalDurationSeconds);

        $currentLat = $pickupLat + ($deliveryLat - $pickupLat) * $progress;
        $currentLng = $pickupLng + ($deliveryLng - $pickupLng) * $progress;

        return [
            'latitude' => $currentLat,
            'longitude' => $currentLng,
            'timestamp' => now()->toIso8601String(),
        ];
    }
}
