@extends('layouts.app')

@section('title', 'Automation Management')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Automation Management</h1>
                    <p class="text-gray-600 mt-1">Monitor and control automated processes</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <button onclick="refreshAutomationData()" 
                            class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh Data
                    </button>
                    <button onclick="runHealthCheck()" 
                            class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-heartbeat mr-2"></i>Health Check
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        
        <!-- System Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Automation Status -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-robot text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Automation Status</p>
                        <p class="text-2xl font-bold text-green-600" id="automation-status">Active</p>
                    </div>
                </div>
            </div>

            <!-- Success Rate -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-bold text-blue-600" id="success-rate">--</p>
                    </div>
                </div>
            </div>

            <!-- Auto Assignments -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-user-check text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Auto Assignments</p>
                        <p class="text-2xl font-bold text-purple-600" id="auto-assignments">--</p>
                    </div>
                </div>
            </div>

            <!-- Pending Actions -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Actions</p>
                        <p class="text-2xl font-bold text-yellow-600" id="pending-actions">--</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Automation Services -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Automation Services</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="automation-services">
                        <!-- Services will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Recent Activities</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="recent-activities">
                        <!-- Activities will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Tables -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Assignment Statistics -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Assignment Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="assignment-stats">
                        <!-- Assignment stats will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- System Performance -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">System Performance</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4" id="system-performance">
                        <!-- Performance metrics will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Controls -->
        <div class="mt-8 bg-white rounded-xl shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-bold text-gray-900">Manual Controls</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="triggerBulkAssignment()" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-users mr-2"></i>Bulk Assignment
                    </button>
                    <button onclick="recalculateRoutes()" 
                            class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-route mr-2"></i>Recalculate Routes
                    </button>
                    <button onclick="syncDashboards()" 
                            class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-sync mr-2"></i>Sync Dashboards
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loading-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
            <span class="ml-3 text-gray-700">Processing...</span>
        </div>
    </div>
</div>

<script>
// Auto-refresh data every 30 seconds
setInterval(refreshAutomationData, 30000);

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    refreshAutomationData();
});

function refreshAutomationData() {
    fetch('/api/automation/stats')
        .then(response => response.json())
        .then(data => {
            updateDashboard(data);
        })
        .catch(error => {
            console.error('Error fetching automation data:', error);
        });
}

function updateDashboard(data) {
    // Update status cards
    if (data.system) {
        document.getElementById('success-rate').textContent = data.system.automation_success_rate + '%';
        document.getElementById('auto-assignments').textContent = data.system.auto_assigned_today;
        document.getElementById('pending-actions').textContent = data.system.pending_assignments;
    }

    // Update automation services
    updateAutomationServices(data);
    
    // Update assignment statistics
    updateAssignmentStats(data.assignment || {});
    
    // Update system performance
    updateSystemPerformance(data);
}

function updateAutomationServices(data) {
    const container = document.getElementById('automation-services');
    const services = [
        { name: 'Route Optimization', status: 'active', description: 'Calculating optimal delivery routes' },
        { name: 'Notifications', status: 'active', description: 'Sending automated notifications' }
    ];

    container.innerHTML = services.map(service => `
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
                <h4 class="font-medium text-gray-900">${service.name}</h4>
                <p class="text-sm text-gray-600">${service.description}</p>
            </div>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ${service.status}
            </span>
        </div>
    `).join('');
}

function updateAssignmentStats(stats) {
    const container = document.getElementById('assignment-stats');
    container.innerHTML = `
        <div class="space-y-3">
            <div class="flex justify-between">
                <span class="text-gray-600">Total Assignments</span>
                <span class="font-medium">${stats.total_auto_assignments || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Successful Assignments</span>
                <span class="font-medium">${stats.successful_assignments || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Average Assignment Time</span>
                <span class="font-medium">${Math.round(stats.average_assignment_time || 0)} min</span>
            </div>
        </div>
    `;
}

function updateSystemPerformance(data) {
    const container = document.getElementById('system-performance');
    container.innerHTML = `
        <div class="space-y-3">
            <div class="flex justify-between">
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Active Deliveries</span>
                <span class="font-medium">${data.system?.active_deliveries || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">API Calls Today</span>
                <span class="font-medium">${data.routing?.api_calls_today || 0}</span>
            </div>
        </div>
    `;
}

function runHealthCheck() {
    showLoading();
    
    fetch('/api/automation/health')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            showHealthCheckResults(data);
        })
        .catch(error => {
            hideLoading();
            alert('Health check failed: ' + error.message);
        });
}

function showHealthCheckResults(data) {
    const status = data.status === 'healthy' ? 'All systems operational' : 'Some issues detected';
    const color = data.status === 'healthy' ? 'text-green-600' : 'text-red-600';
    
    alert(`System Health: ${status}\nStatus: ${data.status}\nTimestamp: ${data.timestamp}`);
}

function triggerBulkAssignment() {
    if (confirm('Trigger bulk assignment for all pending bookings?')) {
        showLoading();
        // Implementation would call bulk assignment API
        setTimeout(() => {
            hideLoading();
            alert('Bulk assignment initiated');
        }, 2000);
    }
}

function recalculateRoutes() {
    if (confirm('Recalculate routes for all active deliveries?')) {
        showLoading();
        // Implementation would call route recalculation API
        setTimeout(() => {
            hideLoading();
            alert('Route recalculation initiated');
        }, 2000);
    }
}

function syncDashboards() {
    showLoading();
    // Implementation would call dashboard sync API
    setTimeout(() => {
        hideLoading();
        alert('Dashboard sync completed');
        refreshAutomationData();
    }, 1000);
}

function showLoading() {
    document.getElementById('loading-modal').classList.remove('hidden');
    document.getElementById('loading-modal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loading-modal').classList.add('hidden');
    document.getElementById('loading-modal').classList.remove('flex');
}
</script>
@endsection
