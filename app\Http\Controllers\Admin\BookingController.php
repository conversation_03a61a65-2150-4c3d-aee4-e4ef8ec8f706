<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;

use Illuminate\Http\Request;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings
     */
    public function index(Request $request)
    {
        $query = Booking::with(['customer']);
        
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        // Search by booking ID or customer name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('booking_id', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }
        
        $bookings = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Get status counts for filter tabs
        $statusCounts = [
            'all' => Booking::count(),
            'pending' => Booking::where('status', 'pending')->count(),
            'confirmed' => Booking::where('status', 'confirmed')->count(),

            'in_progress' => Booking::where('status', 'in_progress')->count(),
            'completed' => Booking::where('status', 'completed')->count(),
            'cancelled' => Booking::where('status', 'cancelled')->count(),
        ];
        
        return view('admin.bookings.index', compact('bookings', 'statusCounts'));
    }
    
    /**
     * Display the specified booking
     */
    public function show(Booking $booking)
    {
        $booking->load(['customer', 'payment', 'review']);

        return view('admin.bookings.show', compact('booking'));
    }
    
    /**
     * Update booking status
     */
    public function updateStatus(Request $request, Booking $booking)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,in_progress,delivered,cancelled',
            'cancellation_reason' => 'nullable|string|max:500'
        ]);
        
        $updateData = ['status' => $request->status];
        
        if ($request->status === 'cancelled' && $request->filled('cancellation_reason')) {
            $updateData['cancellation_reason'] = $request->cancellation_reason;
        }
        
        if ($request->status === 'completed') {
            $updateData['delivered_at'] = now();
        }
        
        $booking->update($updateData);
        
        return back()->with('success', 'Booking status updated successfully!');
    }

}
