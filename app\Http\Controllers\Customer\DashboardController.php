<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Show the customer dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get recent bookings
        $recentBookings = $user->bookings()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // Get booking statistics
        $stats = [
            'total_bookings' => $user->bookings()->count(),
            'pending_bookings' => $user->bookings()->where('status', 'pending')->count(),
            'in_transit_bookings' => $user->bookings()->whereIn('status', ['confirmed', 'in_progress'])->count(),
            'delivered_bookings' => $user->bookings()->where('status', 'delivered')->count(),
        ];
        
        // Get unread notifications
        $unreadNotifications = $user->notifications()
            ->unread()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // Get active booking (if any)
        $activeBooking = $user->bookings()
            ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
            ->first();
        
        return view('customer.dashboard', compact(
            'recentBookings', 
            'stats', 
            'unreadNotifications', 
            'activeBooking'
        ));
    }
    
    /**
     * Show notifications page
     */
    public function notifications()
    {
        $user = Auth::user();
        
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        
        return view('customer.notifications', compact('notifications'));
    }
    
    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(Notification $notification)
    {
        // Ensure user can only mark their own notifications as read
        if ($notification->user_id !== Auth::id()) {
            abort(403);
        }
        
        $notification->markAsRead();
        
        return response()->json(['success' => true]);
    }
}
