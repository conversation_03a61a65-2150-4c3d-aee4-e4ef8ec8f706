<?php

namespace App\Events;

use App\Models\Booking;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $booking;

    /**
     * Create a new event instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('bookings'),
            new PrivateChannel('admin-dashboard'),
            new PrivateChannel('live-map'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_reference' => $this->booking->booking_id,
            'customer_id' => $this->booking->user_id,
            'pickup_location' => [
                'address' => $this->booking->pickup_address,
                'latitude' => $this->booking->pickup_latitude,
                'longitude' => $this->booking->pickup_longitude,
            ],
            'delivery_location' => [
                'address' => $this->booking->delivery_address,
                'latitude' => $this->booking->delivery_latitude,
                'longitude' => $this->booking->delivery_longitude,
            ],
            'package_type' => $this->booking->package_type,
            'estimated_cost' => $this->booking->estimated_cost,
            'created_at' => $this->booking->created_at->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'booking.created';
    }
}
