# TTAJet Laravel Setup Script for Windows
# Run this script as Administrator for best results

Write-Host "Setting up TTAJet Laravel Application..." -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Create required directories
$directories = @(
    "bootstrap\cache",
    "storage\app",
    "storage\app\public", 
    "storage\framework",
    "storage\framework\cache",
    "storage\framework\cache\data",
    "storage\framework\sessions",
    "storage\framework\views",
    "storage\logs"
)

Write-Host "`nCreating required directories..." -ForegroundColor Yellow
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created: $dir" -ForegroundColor Green
    } else {
        Write-Host "Exists: $dir" -ForegroundColor Gray
    }
}

# Set permissions for Laravel directories
Write-Host "`nSetting directory permissions..." -ForegroundColor Yellow
try {
    # Give full control to current user and system
    $acl = Get-Acl "bootstrap\cache"
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($env:USERNAME, "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl "bootstrap\cache" $acl
    
    $acl = Get-Acl "storage"
    $acl.SetAccessRule($accessRule)
    Set-Acl "storage" $acl
    
    Write-Host "Permissions set successfully!" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not set permissions. You may need to run as Administrator." -ForegroundColor Yellow
}

# Check if Composer is installed
Write-Host "`nChecking Composer installation..." -ForegroundColor Yellow
try {
    $composerVersion = composer --version 2>$null
    if ($composerVersion) {
        Write-Host "Composer found: $composerVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "Composer not found. Please install Composer first." -ForegroundColor Red
    Write-Host "Download from: https://getcomposer.org/download/" -ForegroundColor Yellow
    exit 1
}

# Check if vendor directory exists
if (!(Test-Path "vendor")) {
    Write-Host "`nInstalling Composer dependencies..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Gray
    
    try {
        composer install --no-dev --optimize-autoloader
        Write-Host "Dependencies installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Error installing dependencies. Please run 'composer install' manually." -ForegroundColor Red
    }
} else {
    Write-Host "`nComposer dependencies already installed." -ForegroundColor Green
}

# Generate application key if not set
Write-Host "`nChecking application key..." -ForegroundColor Yellow
$envContent = Get-Content ".env" -ErrorAction SilentlyContinue
if ($envContent -and ($envContent | Select-String "APP_KEY=base64:")) {
    $keyLine = $envContent | Select-String "APP_KEY="
    if ($keyLine -match "APP_KEY=base64:[A-Za-z0-9+/=]+") {
        Write-Host "Application key already set." -ForegroundColor Green
    } else {
        Write-Host "Generating application key..." -ForegroundColor Yellow
        try {
            php artisan key:generate
            Write-Host "Application key generated!" -ForegroundColor Green
        } catch {
            Write-Host "Error generating key. Please run 'php artisan key:generate' manually." -ForegroundColor Red
        }
    }
} else {
    Write-Host "No .env file found or APP_KEY not set." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Write-Host "Copying .env.example to .env..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
        Write-Host "Please configure your database settings in .env file." -ForegroundColor Yellow
    }
}

# Check database connection
Write-Host "`nChecking database configuration..." -ForegroundColor Yellow
if (Test-Path ".env") {
    $envContent = Get-Content ".env"
    $dbDatabase = ($envContent | Select-String "DB_DATABASE=").ToString().Split("=")[1]
    if ($dbDatabase -and $dbDatabase -ne "") {
        Write-Host "Database configured: $dbDatabase" -ForegroundColor Green
        Write-Host "Make sure to create this database in phpMyAdmin!" -ForegroundColor Yellow
    } else {
        Write-Host "Please configure database settings in .env file." -ForegroundColor Yellow
    }
}

Write-Host "`n=======================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Create database 'ttajet_courier' in phpMyAdmin" -ForegroundColor White
Write-Host "2. Configure database settings in .env file" -ForegroundColor White
Write-Host "3. Run: php artisan migrate --seed" -ForegroundColor White
Write-Host "4. Visit: http://localhost" -ForegroundColor White

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
