<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTAJet Courier Service</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #F9FAFB; /* Use a light gray for the base */
        }
        .brand-orange {
            background-color: #F97316;
        }
        .brand-orange-text {
            color: #F97316;
        }
        /* Enhanced Glassmorphism Effect */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        #pricing {
            background-image: linear-gradient(to top right, #1f2937, #111827);
        }
        .hero-section {
            background-image: url('https://img.freepik.com/free-vector/worldwide-global-map-outline-black-background_1017-46153.jpg');
            background-size: cover;
            background-position: center;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div id="app">
        <header class="bg-black/50 text-white fixed top-0 w-full z-50 backdrop-blur-sm">
            <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
                <a href="index.html" class="text-2xl font-bold cursor-pointer">
                    TTAJET.
                    <span class="block text-xs font-normal text-gray-400">Courier Service</span>
                </a>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="about.html" class="text-gray-300 hover:text-white transition-colors">About</a>
                    <a href="#services" class="text-gray-300 hover:text-white transition-colors">Services</a>
                    <a href="booking.html" class="text-gray-300 hover:text-white transition-colors">Book a Delivery</a>
                    <a href="dashboard.html" class="bg-orange-600 px-5 py-2.5 rounded-lg text-sm font-semibold hover:bg-orange-700 transition-colors">Admin Dashboard</a>
                </div>
                <div class="md:hidden">
                    <button class="text-white focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </nav>
        </header>

        <main>
            <section class="hero-section relative text-white min-h-screen flex items-center">
                <div class="absolute inset-0 bg-black/80"></div>
                <div class="container mx-auto px-6 relative z-10">
                    <div class="grid lg:grid-cols-2 gap-12 items-center">
                        <div class="text-content">
                             <h1 class="text-5xl md:text-7xl font-extrabold leading-tight">Fast & Reliable Courier Services</h1>
                             <p class="mt-6 text-lg text-gray-300 max-w-xl">Book a courier with TTA Jet for quick, secure, and professional delivery service across Accra.</p>
                             <div class="mt-10 flex flex-col sm:flex-row gap-4">
                                <a href="dashboard.html" class="bg-orange-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-700 transition-colors text-center">Join Now</a>
                                <a href="about.html" class="bg-white/10 border border-white/20 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/20 transition-colors text-center">About Us</a>
                             </div>
                        </div>
                        <div class="booking-form glass-card rounded-2xl p-8 shadow-2xl text-white">
                            <h2 class="text-2xl font-bold mb-6">Quick Booking</h2>
                            <form id="quick-booking-form" class="space-y-4" action="booking.html" method="get">
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
                                    <input type="text" placeholder="Enter pickup location" class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
                                    <input type="text" placeholder="Enter delivery location" class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-box absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
                                    <select class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none">
                                        <option class="bg-gray-800">Select package type</option>
                                        <option class="bg-gray-800">Small Box</option>
                                        <option class="bg-gray-800">Medium Box</option>
                                        <option class="bg-gray-800">Large Box</option>
                                        <option class="bg-gray-800">Document</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
                                </div>
                                <button type="submit" class="w-full bg-orange-600 text-white font-bold py-3 rounded-lg hover:bg-orange-700 transition-colors">
                                    Get Started
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

            <section id="about-preview" class="py-20 md:py-28 bg-gray-50">
                <div class="container mx-auto px-6">
                    <div class="max-w-4xl mx-auto text-center">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Your Trusted Partner in Delivery</h2>
                        <p class="mt-4 text-lg text-gray-600">TTA Jet is your trusted partner for fast and reliable courier delivery within Accra. We're committed to providing exceptional service with every package we handle. Our mission is to bridge distances, connecting people and businesses with efficiency and care.</p>
                        <a href="about.html" class="mt-8 inline-block font-semibold brand-orange-text hover:underline text-lg">Learn More About Us <i class="fas fa-arrow-right ml-1 text-sm"></i></a>
                    </div>
                </div>
            </section>

            <section id="services" class="bg-gray-100 py-20 md:py-28">
                <div class="container mx-auto px-6">
                     <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center text-gray-900">Our Core Services</h2>
                     <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div class="service-card text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-shipping-fast text-4xl brand-orange-text"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900">Same-Day Delivery</h3>
                            <p class="text-gray-600 mt-2">Get your packages delivered on the same day with unparalleled speed and care.</p>
                        </div>
                        <div class="service-card text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-box-open text-4xl brand-orange-text"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900">Small Packages</h3>
                            <p class="text-gray-600 mt-2">We handle all your small parcels with maximum efficiency and security.</p>
                        </div>
                        <div class="service-card text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                            <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-store text-4xl brand-orange-text"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900">Business Solutions</h3>
                            <p class="text-gray-600 mt-2">Tailored courier services to meet the demands of your small business.</p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="pricing" class="py-20 md:py-28">
                <div class="container mx-auto px-6">
                    <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center text-white">Simple, Transparent Pricing</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <div class="price-card text-center p-8 rounded-2xl glass-card text-white shadow-lg">
                            <i class="fas fa-file-alt fa-3x mb-6 text-white opacity-70"></i>
                            <h3 class="text-2xl font-bold">Document</h3>
                            <p class="opacity-70 text-sm mt-1">&lt; 0.5 kg</p>
                            <p class="text-5xl font-extrabold my-6">CF$15</p>
                            <a href="booking.html" class="bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg transition-colors w-full block">Book Now</a>
                        </div>
                        <div class="price-card text-center p-8 rounded-2xl bg-white text-gray-900 shadow-2xl relative scale-105">
                            <div class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-orange-600 text-white font-bold text-xs px-3 py-1 rounded-full uppercase tracking-wider">Most Popular</div>
                            <i class="fas fa-box fa-3x mb-6 text-orange-600"></i>
                            <h3 class="text-2xl font-bold">Small Box</h3>
                            <p class="text-gray-500 text-sm mt-1">0.5 - 2 kg</p>
                            <p class="text-5xl font-extrabold my-6 text-orange-600">CF$25</p>
                            <a href="booking.html" class="bg-orange-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-orange-700 transition-colors w-full block">Book Now</a>
                        </div>
                        <div class="price-card text-center p-8 rounded-2xl glass-card text-white shadow-lg">
                            <i class="fas fa-box-open fa-3x mb-6 text-white opacity-70"></i>
                            <h3 class="text-2xl font-bold">Medium Box</h3>
                            <p class="opacity-70 text-sm mt-1">2 - 5 kg</p>
                            <p class="text-5xl font-extrabold my-6">CF$40</p>
                            <a href="booking.html" class="bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg transition-colors w-full block">Book Now</a>
                        </div>
                        <div class="price-card text-center p-8 rounded-2xl glass-card text-white shadow-lg">
                            <i class="fas fa-boxes fa-3x mb-6 text-white opacity-70"></i>
                            <h3 class="text-2xl font-bold">Large Box</h3>
                            <p class="opacity-70 text-sm mt-1">&gt; 5 kg</p>
                            <p class="text-5xl font-extrabold my-6">CF$55+</p>
                            <a href="booking.html" class="bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg transition-colors w-full block">Book Now</a>
                        </div>
                    </div>
                </div>
            </section>

            <section id="reviews" class="bg-white py-20 md:py-28">
                <div class="container mx-auto px-6">
                    <div class="grid lg:grid-cols-2 gap-16 items-center">
                        <div class="review-text-content">
                            <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Discover What Our Clients Say About Us!</h2>
                            <p class="mt-4 text-lg text-gray-600">Our platform involves a comprehensive approach focused on attracting new customers while ensuring the loyalty and satisfaction of existing ones.</p>
                        </div>
                        <div class="relative items-center justify-center">
                            <div class="flex items-center justify-center">
                                <div id="review-card" class="bg-white p-8 rounded-2xl shadow-2xl border absolute w-full max-w-md">
                                    <p id="reviewer-quote" class="text-lg text-gray-700">"As a busy professional, managing shipments can be challenging, but TTAJet simplifies this task by providing me with the tools to stay on top of my logistics."</p>
                                    <div class="mt-4 border-t pt-4">
                                        <p id="reviewer-name" class="font-bold text-gray-900">Ama Serwaa</p>
                                        <p id="reviewer-role" class="text-sm text-gray-500">CEO, Serwaa Enterprises</p>
                                    </div>
                                </div>
                            </div>
                            <button id="prev-review" class="absolute top-1/2 -translate-y-1/2 -left-6 w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="next-review" class="absolute top-1/2 -translate-y-1/2 -right-6 w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <footer class="bg-gray-900 text-gray-400">
        <div class="container mx-auto px-6 pt-20">
            <div class="bg-gray-800 p-8 md:p-12 rounded-2xl mb-20 text-center max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-white">Newsletter Signup</h2>
                <p class="text-gray-400 mt-2">Stay in the loop. Get updates, tips & exclusive offers.</p>
                <form class="mt-6 flex flex-col sm:flex-row gap-3 max-w-lg mx-auto">
                    <input type="email" placeholder="ex. <EMAIL>" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-orange-500">
                    <button type="submit" class="bg-orange-600 text-white font-bold px-8 py-3 rounded-lg hover:bg-orange-700 transition-colors shrink-0">Subscribe</button>
                </form>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-12 pb-12">
                <div>
                    <h3 class="text-xl font-bold text-white">TTAJET.</h3>
                    <p class="mt-2 text-sm">Fast & Reliable Courier Service</p>
                    <p class="mt-4 text-sm">123 Smart Lane, Tech City, FR<br>+233 12 345 6789</p>
                    <div class="flex space-x-3 mt-4">
                        <a href="#" class="w-9 h-9 bg-gray-700 rounded-full flex items-center justify-center hover:bg-orange-600 hover:text-white transition-colors"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="w-9 h-9 bg-gray-700 rounded-full flex items-center justify-center hover:bg-orange-600 hover:text-white transition-colors"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="w-9 h-9 bg-gray-700 rounded-full flex items-center justify-center hover:bg-orange-600 hover:text-white transition-colors"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="w-9 h-9 bg-gray-700 rounded-full flex items-center justify-center hover:bg-orange-600 hover:text-white transition-colors"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold text-white tracking-wider">Product</h3>
                    <ul class="mt-4 space-y-3 text-sm">
                        <li><a href="#services" class="hover:text-white">Services</a></li>
                        <li><a href="#pricing" class="hover:text-white">Pricing</a></li>
                        <li><a href="booking.html" class="hover:text-white">Book a Delivery</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-white tracking-wider">Company</h3>
                    <ul class="mt-4 space-y-3 text-sm">
                        <li><a href="about.html" class="hover:text-white">About Us</a></li>
                        <li><a href="#" class="hover:text-white">Careers</a></li>
                        <li><a href="#" class="hover:text-white">Press</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-white tracking-wider">Support</h3>
                    <ul class="mt-4 space-y-3 text-sm">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Contact Support</a></li>
                        <li><a href="#" class="hover:text-white">Warranty Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 py-6 flex flex-col sm:flex-row justify-between items-center">
                <p class="text-sm text-gray-500">&copy; 2025 TTAJet Technologies. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <button id="scroll-to-top" class="fixed bottom-8 right-8 z-50 w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center text-white shadow-lg hover:bg-orange-700 transition-all duration-300 opacity-0 invisible scale-90">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            gsap.registerPlugin(ScrollTrigger);

            const tl = gsap.timeline({defaults: {ease: 'power3.out'}});
            tl.from('.text-content > *', {opacity: 0, y: 30, stagger: 0.2, duration: 1})
              .from('.booking-form', {opacity: 0, scale: 0.95, duration: 0.8}, "-=0.6");

            gsap.from("#about-preview > *", {
                scrollTrigger: { trigger: "#about-preview", start: "top 80%", toggleActions: "play none none none" },
                opacity: 0, y: 50, stagger: 0.2, duration: 1
            });
            
            gsap.from(".service-card", {
                scrollTrigger: { trigger: "#services", start: "top 80%", toggleActions: "play none none none" },
                opacity: 1, y: 50, stagger: 0.2, duration: 0.8
            });

            gsap.from(".price-card", {
                scrollTrigger: { trigger: "#pricing", start: "top 80%", toggleActions: "play none none none" },
                opacity: 0, y: 50, stagger: 0.2, duration: 0.8
            });

            gsap.from("#reviews .review-text-content > *, #reviews .relative", {
                scrollTrigger: { trigger: "#reviews", start: "top 80%", toggleActions: "play none none none" },
                opacity: 0, y: 50, stagger: 0.3, duration: 1
            });

            const reviewsData = [
                {
                    quote: '"As a busy professional, managing shipments can be challenging, but TTAJet simplifies this task by providing me with the tools to stay on top of my logistics."',
                    name: 'Ama Serwaa',
                    role: 'CEO, Serwaa Enterprises'
                },
                {
                    quote: '"The booking process was so simple and the customer service was excellent. I\'ll definitely be using them again for all my business needs."',
                    name: 'Kofi Mensah',
                    role: 'Founder, Kofi\'s Kreations'
                },
                {
                    quote: '"Great service for my small business. They handle all our deliveries with care and are always on time, which is crucial for customer satisfaction."',
                    name: 'Esi Addo',
                    role: 'Owner, Addo\'s Boutique'
                }
            ];

            let currentReviewIndex = 0;
            const reviewerQuote = document.getElementById('reviewer-quote');
            const reviewerName = document.getElementById('reviewer-name');
            const reviewerRole = document.getElementById('reviewer-role');
            const reviewCard = document.getElementById('review-card');
            const nextBtn = document.getElementById('next-review');
            const prevBtn = document.getElementById('prev-review');

            function showReview(index) {
                const review = reviewsData[index];
                const tl = gsap.timeline();
                tl.to([reviewCard], { opacity: 0, scale: 0.95, duration: 0.3, ease: 'power2.in' })
                  .call(() => {
                    if (reviewerQuote) reviewerQuote.textContent = review.quote;
                    if (reviewerName) reviewerName.textContent = review.name;
                    if (reviewerRole) reviewerRole.textContent = review.role;
                  })
                  .to([reviewCard], { opacity: 1, scale: 1, duration: 0.3, ease: 'power2.out' });
            }

            nextBtn.addEventListener('click', () => {
                currentReviewIndex = (currentReviewIndex + 1) % reviewsData.length;
                showReview(currentReviewIndex);
            });

            prevBtn.addEventListener('click', () => {
                currentReviewIndex = (currentReviewIndex - 1 + reviewsData.length) % reviewsData.length;
                showReview(currentReviewIndex);
            });

            const scrollToTopBtn = document.getElementById('scroll-to-top');
            gsap.to(scrollToTopBtn, {
                scrollTrigger: {
                    trigger: "body",
                    start: "500px top",
                    end: "bottom bottom",
                    toggleActions: "play reverse play reverse",
                },
                opacity: 1,
                visibility: "visible",
                scale: 1,
                duration: 0.3
            });

            scrollToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
