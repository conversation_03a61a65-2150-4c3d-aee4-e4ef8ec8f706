<?php

namespace App\Jobs;

use App\Models\Booking;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class UpdateStatisticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $booking;
    protected $newStatus;

    /**
     * Create a new job instance.
     */
    public function __construct(Booking $booking, string $newStatus)
    {
        $this->booking = $booking;
        $this->newStatus = $newStatus;
        
        $this->timeout = 60; // 1 minute
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Update booking statistics
            $this->updateBookingStatistics();


            // Update customer statistics
            $this->updateCustomerStatistics();

            // Update system-wide statistics
            $this->updateSystemStatistics();

            // Update revenue statistics
            if ($this->newStatus === 'completed') {
                $this->updateRevenueStatistics();
            }

            Log::info("Statistics updated for booking {$this->booking->booking_id} with status {$this->newStatus}");

        } catch (\Exception $e) {
            Log::error("Failed to update statistics for booking {$this->booking->booking_id}: " . $e->getMessage());
        }
    }

    /**
     * Update booking-related statistics
     */
    protected function updateBookingStatistics(): void
    {
        $today = now()->format('Y-m-d');
        
        // Update daily booking counts by status
        $statusKey = "bookings_{$this->newStatus}_{$today}";
        $currentCount = Cache::get($statusKey, 0);
        Cache::put($statusKey, $currentCount + 1, 86400); // 24 hours

        // Update hourly statistics
        $hour = now()->format('Y-m-d-H');
        $hourlyKey = "bookings_hourly_{$hour}";
        $hourlyCount = Cache::get($hourlyKey, 0);
        Cache::put($hourlyKey, $hourlyCount + 1, 86400);

        // Update status transition statistics
        if ($this->newStatus === 'completed') {
            $this->updateCompletionStatistics();
        } elseif ($this->newStatus === 'cancelled') {
            $this->updateCancellationStatistics();
        }
    }


    /**
     * Update customer statistics
     */
    protected function updateCustomerStatistics(): void
    {
        $customer = $this->booking->customer;
        $today = now()->format('Y-m-d');

        // Update customer daily statistics
        $customerStatsKey = "customer_stats_{$customer->id}_{$today}";
        $customerStats = Cache::get($customerStatsKey, [
            'total_bookings' => 0,
            'completed_bookings' => 0,
            'cancelled_bookings' => 0,
            'total_spent' => 0,
        ]);

        $customerStats['total_bookings']++;

        if ($this->newStatus === 'completed') {
            $customerStats['completed_bookings']++;
            $customerStats['total_spent'] += $this->booking->final_cost ?? $this->booking->estimated_cost;
        } elseif ($this->newStatus === 'cancelled') {
            $customerStats['cancelled_bookings']++;
        }

        Cache::put($customerStatsKey, $customerStats, 86400);
    }

    /**
     * Update system-wide statistics
     */
    protected function updateSystemStatistics(): void
    {
        $today = now()->format('Y-m-d');

        // Update system performance metrics
        $systemStatsKey = "system_stats_{$today}";
        $systemStats = Cache::get($systemStatsKey, [
            'total_bookings' => 0,
            'auto_assignments' => 0,
            'manual_assignments' => 0,
            'completion_rate' => 0,
            'average_delivery_time' => 0,
        ]);

        $systemStats['total_bookings']++;

        // Track assignment type (simplified check)

        // Calculate completion rate
        if ($this->newStatus === 'completed') {
            $completedToday = Booking::whereDate('delivered_at', today())->count();
            $totalToday = Booking::whereDate('created_at', today())->count();
            $systemStats['completion_rate'] = $totalToday > 0 ? ($completedToday / $totalToday) * 100 : 0;
        }

        Cache::put($systemStatsKey, $systemStats, 86400);
    }

    /**
     * Update revenue statistics
     */
    protected function updateRevenueStatistics(): void
    {
        $today = now()->format('Y-m-d');
        $revenue = $this->booking->final_cost ?? $this->booking->estimated_cost;

        // Update daily revenue
        $revenueKey = "revenue_{$today}";
        $currentRevenue = Cache::get($revenueKey, 0);
        Cache::put($revenueKey, $currentRevenue + $revenue, 86400);

        // Update monthly revenue
        $month = now()->format('Y-m');
        $monthlyRevenueKey = "revenue_monthly_{$month}";
        $monthlyRevenue = Cache::get($monthlyRevenueKey, 0);
        Cache::put($monthlyRevenueKey, $monthlyRevenue + $revenue, 86400 * 31);

        // Update revenue by package type
        $packageRevenueKey = "revenue_package_{$this->booking->package_type}_{$today}";
        $packageRevenue = Cache::get($packageRevenueKey, 0);
        Cache::put($packageRevenueKey, $packageRevenue + $revenue, 86400);
    }

    /**
     * Update completion statistics
     */
    protected function updateCompletionStatistics(): void
    {
        $deliveryTime = $this->booking->delivered_at;
        $createdTime = $this->booking->created_at;
        
        if ($deliveryTime && $createdTime) {
            $totalMinutes = $deliveryTime->diffInMinutes($createdTime);
            
            // Update average delivery time
            $avgKey = "average_delivery_time";
            $currentAvg = Cache::get($avgKey, 0);
            $completedCount = Cache::get("completed_bookings_count", 0) + 1;
            
            $newAvg = (($currentAvg * ($completedCount - 1)) + $totalMinutes) / $completedCount;
            
            Cache::put($avgKey, $newAvg, 86400);
            Cache::put("completed_bookings_count", $completedCount, 86400);
        }
    }

    /**
     * Update cancellation statistics
     */
    protected function updateCancellationStatistics(): void
    {
        $today = now()->format('Y-m-d');
        
        // Track cancellation reasons
        if ($this->booking->cancellation_reason) {
            $reasonKey = "cancellation_reasons_{$today}";
            $reasons = Cache::get($reasonKey, []);
            $reason = strtolower(trim($this->booking->cancellation_reason));
            $reasons[$reason] = ($reasons[$reason] ?? 0) + 1;
            Cache::put($reasonKey, $reasons, 86400);
        }

        // Update cancellation rate
        $totalToday = Booking::whereDate('created_at', today())->count();
        $cancelledToday = Booking::whereDate('created_at', today())
            ->where('status', 'cancelled')
            ->count();
            
        $cancellationRate = $totalToday > 0 ? ($cancelledToday / $totalToday) * 100 : 0;
        Cache::put("cancellation_rate_{$today}", $cancellationRate, 86400);
    }


    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("UpdateStatisticsJob failed for booking {$this->booking->booking_id}: " . $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'booking:' . $this->booking->id,
            'statistics',
            'automation'
        ];
    }
}
