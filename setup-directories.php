<?php

/**
 * TTAJet Laravel Directory Setup Script
 * This script creates the required Laravel directories and sets basic permissions
 */

echo "TTAJet Laravel Directory Setup\n";
echo "==============================\n\n";

// Required directories for Laravel
$directories = [
    'bootstrap/cache',
    'storage/app',
    'storage/app/public',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs'
];

$created = 0;
$existing = 0;

echo "Creating required directories...\n";

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✓ Created: $dir\n";
            $created++;
        } else {
            echo "✗ Failed to create: $dir\n";
        }
    } else {
        echo "- Exists: $dir\n";
        $existing++;
    }
}

echo "\nSetting directory permissions...\n";

// Set permissions for critical directories
$permissionDirs = ['bootstrap/cache', 'storage'];

foreach ($permissionDirs as $dir) {
    if (is_dir($dir)) {
        // Try to set permissions (may not work on all systems)
        if (chmod($dir, 0755)) {
            echo "✓ Set permissions for: $dir\n";
        } else {
            echo "- Could not set permissions for: $dir (this may be normal on Windows)\n";
        }
        
        // Try to make it writable
        if (is_writable($dir)) {
            echo "✓ Directory is writable: $dir\n";
        } else {
            echo "⚠ Directory may not be writable: $dir\n";
        }
    }
}

echo "\n==============================\n";
echo "Setup Summary:\n";
echo "- Created directories: $created\n";
echo "- Existing directories: $existing\n";

// Check if .env file exists
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        echo "\n⚠ .env file not found. Copying from .env.example...\n";
        if (copy('.env.example', '.env')) {
            echo "✓ .env file created successfully!\n";
        } else {
            echo "✗ Failed to create .env file\n";
        }
    } else {
        echo "\n⚠ Neither .env nor .env.example found\n";
    }
} else {
    echo "\n✓ .env file exists\n";
}

// Check if vendor directory exists
if (!is_dir('vendor')) {
    echo "\n⚠ Vendor directory not found. Please run: composer install\n";
} else {
    echo "\n✓ Vendor directory exists\n";
}

echo "\nNext steps:\n";
echo "1. Run: composer install (if not done already)\n";
echo "2. Run: php artisan key:generate\n";
echo "3. Configure database settings in .env\n";
echo "4. Create database 'ttajet_courier' in phpMyAdmin\n";
echo "5. Run: php artisan migrate --seed\n";
echo "6. Visit: http://localhost\n";

echo "\n==============================\n";
echo "Setup complete!\n";

?>
