@props(['action' => '#', 'method' => 'GET'])

<x-glass-card>
    <h2 class="text-2xl font-bold mb-6">Quick Booking</h2>
    <form id="quick-booking-form" class="space-y-4" action="{{ $action }}" method="{{ $method }}">
        @if($method !== 'GET')
            @csrf
        @endif
        
        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
            <input type="text" name="pickup_location" placeholder="Enter pickup location" 
                   class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500">
        </div>
        
        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
            <input type="text" name="delivery_location" placeholder="Enter delivery location" 
                   class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500">
        </div>
        
        <div class="relative">
            <i class="fas fa-box absolute left-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
            <select name="package_type" class="w-full p-3 pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none">
                <option class="bg-gray-800">Select package type</option>
                <option class="bg-gray-800" value="document">Document</option>
                <option class="bg-gray-800" value="small_box">Small Box</option>
                <option class="bg-gray-800" value="medium_box">Medium Box</option>
                <option class="bg-gray-800" value="large_box">Large Box</option>
            </select>
            <i class="fas fa-chevron-down absolute right-4 top-1/2 -translate-y-1/2 text-gray-300"></i>
        </div>
        
        <button type="submit" class="w-full bg-orange-600 text-white font-bold py-3 rounded-lg hover:bg-orange-700 transition-colors">
            Get Started
        </button>
    </form>
</x-glass-card>
