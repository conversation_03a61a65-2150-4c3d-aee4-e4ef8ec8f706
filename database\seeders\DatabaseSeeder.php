<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Booking;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'phone_number' => '+233123456789',
                'role' => 'admin',
                'is_active' => true,
            ]
        );

        // Create Sample Customer
        $customer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'password' => Hash::make('password'),
                'phone_number' => '+233987654321',
                'role' => 'customer',
                'is_active' => true,
            ]
        );



        // Create Sample Bookings
        $bookings = [
            [
                'user_id' => $customer->id,

                'pickup_address' => '123 First St, Accra',
                'pickup_latitude' => 5.6037,
                'pickup_longitude' => -0.1870,
                'pickup_person_name' => '<PERSON>',
                'pickup_person_phone' => '+233987654321',
                'delivery_address' => '456 Market Rd, Accra',
                'delivery_latitude' => 5.6150,
                'delivery_longitude' => -0.1950,
                'receiver_name' => 'Jane Smith',
                'receiver_phone' => '+233555999888',
                'package_type' => 'small',
                'package_weight' => 2.5,
                'estimated_cost' => 25.00,
                'payment_method' => 'mobile_money',
                'pickup_time_preference' => 'now',
                'status' => 'delivered',
                'distance_km' => 3.2,
                'delivered_at' => now()->subDays(1),
            ],
            [
                'user_id' => $customer->id,
                'pickup_address' => '789 Oak Ave, Accra',
                'pickup_latitude' => 5.5900,
                'pickup_longitude' => -0.2000,
                'pickup_person_name' => 'John Doe',
                'pickup_person_phone' => '+233987654321',
                'delivery_address' => '321 Elm St, Accra',
                'delivery_latitude' => 5.6200,
                'delivery_longitude' => -0.1800,
                'receiver_name' => 'Bob Johnson',
                'receiver_phone' => '+233555777666',
                'package_type' => 'medium',
                'package_weight' => 5.0,
                'estimated_cost' => 45.00,
                'payment_method' => 'card',
                'pickup_time_preference' => '1_hour',
                'status' => 'in_progress',
                'distance_km' => 5.8,

                'actual_pickup_time' => now()->subHours(2),
            ],
            [
                'user_id' => $customer->id,
                'pickup_address' => '555 Pine Ln, Accra',
                'pickup_latitude' => 5.5800,
                'pickup_longitude' => -0.2100,
                'pickup_person_name' => 'John Doe',
                'pickup_person_phone' => '+233987654321',
                'delivery_address' => '888 Maple Dr, Accra',
                'delivery_latitude' => 5.6300,
                'delivery_longitude' => -0.1700,
                'receiver_name' => 'Alice Brown',
                'receiver_phone' => '+233555444333',
                'package_type' => 'document',
                'estimated_cost' => 20.00,
                'payment_method' => 'cash',
                'pickup_time_preference' => 'now',
                'status' => 'pending',
                'distance_km' => 4.1,
            ],
        ];

        foreach ($bookings as $bookingData) {
            Booking::create($bookingData);
        }



        // Create additional customers for testing
        User::factory(10)->create([
            'role' => 'customer',
            'is_active' => true,
        ]);


    }
}
