<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notificationData;

    /**
     * Create a new job instance.
     */
    public function __construct(array $notificationData)
    {
        $this->notificationData = $notificationData;
        $this->timeout = 60; // 1 minute timeout
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (isset($this->notificationData['user_id'])) {
                // Send to specific user
                $this->sendToUser($this->notificationData['user_id']);
            } elseif (isset($this->notificationData['role'])) {
                // Send to all users with specific role
                $this->sendToRole($this->notificationData['role']);
            } else {
                Log::warning('Notification data missing user_id or role', $this->notificationData);
                return;
            }

        } catch (\Exception $e) {
            Log::error("Failed to send notification: " . $e->getMessage(), [
                'notification_data' => $this->notificationData,
                'error' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Send notification to a specific user
     */
    protected function sendToUser(int $userId): void
    {
        $user = User::find($userId);
        
        if (!$user) {
            Log::warning("User not found for notification", ['user_id' => $userId]);
            return;
        }

        $this->createNotificationRecord($user);
        $this->sendPushNotification($user);
        $this->sendEmailIfRequired($user);
    }

    /**
     * Send notification to all users with a specific role
     */
    protected function sendToRole(string $role): void
    {
        $users = User::where('role', $role)->where('is_active', true)->get();
        
        foreach ($users as $user) {
            $this->createNotificationRecord($user);
            $this->sendPushNotification($user);
            $this->sendEmailIfRequired($user);
        }
    }

    /**
     * Create notification record in database
     */
    protected function createNotificationRecord(User $user): void
    {
        $notification = $user->notifications()->create([
            'title' => $this->notificationData['title'],
            'message' => $this->notificationData['message'],
            'type' => $this->notificationData['type'] ?? 'general',
            'priority' => $this->notificationData['priority'] ?? 'normal',
            'data' => $this->notificationData['data'] ?? [],
            'is_read' => false,
        ]);

        Log::info("Notification created for user {$user->id}: {$notification->title}");
    }

    /**
     * Send push notification
     */
    protected function sendPushNotification(User $user): void
    {
        // This would integrate with a push notification service like FCM, Pusher, etc.
        // For now, we'll just log the push notification
        
        $pushData = [
            'title' => $this->notificationData['title'],
            'body' => $this->notificationData['message'],
            'data' => $this->notificationData['data'] ?? [],
            'priority' => $this->notificationData['priority'] ?? 'normal',
        ];

        // In a real implementation, you would send to FCM, APNS, etc.
        Log::info("Push notification sent to user {$user->id}", $pushData);

        // Example FCM implementation:
        /*
        if ($user->fcm_token) {
            $fcm = new FCMService();
            $fcm->sendToDevice($user->fcm_token, $pushData);
        }
        */
    }

    /**
     * Send email notification if required
     */
    protected function sendEmailIfRequired(User $user): void
    {
        // Check if email notification is required based on type and user preferences
        $emailTypes = ['booking', 'system', 'payment'];
        $highPriorityTypes = ['critical', 'high'];
        
        $shouldSendEmail = in_array($this->notificationData['type'] ?? '', $emailTypes) ||
                          in_array($this->notificationData['priority'] ?? '', $highPriorityTypes);

        if (!$shouldSendEmail) {
            return;
        }

        try {
            // Send email notification
            $this->sendEmail($user);
        } catch (\Exception $e) {
            Log::error("Failed to send email notification to user {$user->id}: " . $e->getMessage());
        }
    }

    /**
     * Send email notification
     */
    protected function sendEmail(User $user): void
    {
        $emailData = [
            'user' => $user,
            'title' => $this->notificationData['title'],
            'message' => $this->notificationData['message'],
            'type' => $this->notificationData['type'] ?? 'general',
            'data' => $this->notificationData['data'] ?? [],
        ];

        // In a real implementation, you would use a proper email template
        Mail::send('emails.notification', $emailData, function ($message) use ($user) {
            $message->to($user->email, $user->name)
                   ->subject($this->notificationData['title']);
        });

        Log::info("Email notification sent to user {$user->id} at {$user->email}");
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SendNotificationJob failed: " . $exception->getMessage(), [
            'notification_data' => $this->notificationData,
            'error' => $exception->getTraceAsString()
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['notification'];
        
        if (isset($this->notificationData['type'])) {
            $tags[] = 'type:' . $this->notificationData['type'];
        }
        
        if (isset($this->notificationData['priority'])) {
            $tags[] = 'priority:' . $this->notificationData['priority'];
        }
        
        return $tags;
    }
}
