@extends('layouts.app')

@section('title', 'Booking History')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking History</h1>
                    <p class="text-gray-600 mt-1">View and track all your delivery bookings</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('customer.dashboard') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                    <a href="{{ route('booking.create') }}" 
                       class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                        <i class="fas fa-plus mr-2"></i>New Booking
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Booking Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <i class="fas fa-box text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $bookings->total() }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <i class="fas fa-clock text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $bookings->where('status', 'pending')->count() }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100">
                            <i class="fas fa-truck text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">In Transit</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $bookings->whereIn('status', ['confirmed', 'delivery_enroute', 'in_progress'])->count() }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completed</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $bookings->where('status', 'completed')->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings List -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                        <h3 class="text-lg font-bold text-gray-900">All Bookings</h3>
                        
                        <!-- Filter Options -->
                        <div class="mt-4 md:mt-0 flex space-x-4">
                            <select onchange="filterBookings(this.value)" 
                                    class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="delivery_enroute">Delivery Enroute</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                @if($bookings->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Booking Details
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Route
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Cost
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($bookings as $booking)
                                    <tr class="hover:bg-gray-50 booking-row" data-status="{{ $booking->status }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                                                        <i class="fas fa-box text-orange-600"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $booking->booking_id }}</div>
                                                    <div class="text-sm text-gray-500">{{ ucfirst($booking->package_type) }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900">
                                                <div class="flex items-center mb-1">
                                                    <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                                                    <span class="truncate max-w-xs">{{ $booking->pickup_address }}</span>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                                                    <span class="truncate max-w-xs">{{ $booking->delivery_address }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @switch($booking->status)
                                                    @case('pending')
                                                        bg-yellow-100 text-yellow-800
                                                        @break
                                                    @case('confirmed')
                                                        bg-blue-100 text-blue-800
                                                        @break
                                                    @case('in_progress')
                                                        bg-purple-100 text-purple-800
                                                        @break
                                                    @case('completed')
                                                        bg-green-100 text-green-800
                                                        @break
                                                    @case('cancelled')
                                                        bg-red-100 text-red-800
                                                        @break
                                                    @default
                                                        bg-gray-100 text-gray-800
                                                @endswitch
                                            ">
                                                {{ $booking->formatted_status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            CF$ {{ number_format($booking->final_cost ?? $booking->estimated_cost, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div>{{ $booking->created_at->format('M d, Y') }}</div>
                                            <div class="text-xs">{{ $booking->created_at->format('h:i A') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('booking.show', $booking) }}" 
                                                   class="text-orange-600 hover:text-orange-900">
                                                    View
                                                </a>
                                                @if(in_array($booking->status, ['confirmed', 'delivery_enroute', 'in_progress']))
                                                    <a href="{{ route('tracking') }}?booking_id={{ $booking->booking_id }}" 
                                                       class="text-blue-600 hover:text-blue-900">
                                                        Track
                                                    </a>
                                                @endif
                                                @if($booking->status === 'completed' && !$booking->review)
                                                    <button onclick="openReviewModal('{{ $booking->id }}')" 
                                                            class="text-green-600 hover:text-green-900">
                                                        Review
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    @if($bookings->hasPages())
                        <div class="p-6 border-t border-gray-200">
                            {{ $bookings->links() }}
                        </div>
                    @endif
                @else
                    <!-- Empty State -->
                    <div class="p-12 text-center">
                        <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-box-open text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No bookings yet</h3>
                        <p class="text-gray-600 mb-6">Start by creating your first delivery booking.</p>
                        <a href="{{ route('booking.create') }}" 
                           class="inline-flex items-center px-6 py-3 brand-orange text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-plus mr-2"></i>Create Your First Booking
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
// Filter bookings by status
function filterBookings(status) {
    const rows = document.querySelectorAll('.booking-row');
    
    rows.forEach(row => {
        if (status === '' || row.getAttribute('data-status') === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Open review modal (placeholder for future implementation)
function openReviewModal(bookingId) {
    // This would open a review modal
    alert('Review functionality will be implemented soon!');
}

// Auto-refresh for active bookings
document.addEventListener('DOMContentLoaded', function() {
    const activeBookings = document.querySelectorAll('[data-status="confirmed"], [data-status="in_progress"]');
    
    if (activeBookings.length > 0) {
        // Refresh page every 30 seconds if there are active bookings
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    }
});
</script>
@endsection
