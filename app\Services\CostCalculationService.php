<?php

namespace App\Services;

class CostCalculationService
{
    /**
     * Base costs for different package types (in CF$)
     */
    protected $baseCosts = [
        'document' => 15.00,
        'small' => 20.00,
        'medium' => 35.00,
        'large' => 50.00,
    ];

    /**
     * Cost per kilometer (in CF$)
     */
    protected $costPerKm = 2.50;

    /**
     * Cost per kilogram for weight (in CF$)
     */
    protected $costPerKg = 2.50;

    /**
     * Minimum delivery cost (in CF$)
     */
    protected $minimumCost = 15.00;

    /**
     * Calculate the estimated cost for a delivery
     *
     * @param string $packageType
     * @param float $weight
     * @param float $distanceKm
     * @return float
     */
    public function calculateCost(string $packageType, float $weight = 0, float $distanceKm = 0): float
    {
        // Get base cost for package type
        $baseCost = $this->baseCosts[$packageType] ?? $this->baseCosts['small'];

        // Add distance cost
        $distanceCost = $distanceKm * $this->costPerKm;

        // Add weight cost
        $weightCost = $weight * $this->costPerKg;

        // Calculate total
        $totalCost = $baseCost + $distanceCost + $weightCost;

        // Apply minimum cost
        $totalCost = max($totalCost, $this->minimumCost);

        // Round to 2 decimal places
        return round($totalCost, 2);
    }

    /**
     * Get cost breakdown for transparency
     *
     * @param string $packageType
     * @param float $weight
     * @param float $distanceKm
     * @return array
     */
    public function getCostBreakdown(string $packageType, float $weight = 0, float $distanceKm = 0): array
    {
        $baseCost = $this->baseCosts[$packageType] ?? $this->baseCosts['small'];
        $distanceCost = $distanceKm * $this->costPerKm;
        $weightCost = $weight * $this->costPerKg;
        $subtotal = $baseCost + $distanceCost + $weightCost;
        $total = max($subtotal, $this->minimumCost);

        return [
            'base_cost' => $baseCost,
            'distance_cost' => $distanceCost,
            'weight_cost' => $weightCost,
            'subtotal' => $subtotal,
            'minimum_applied' => $total > $subtotal,
            'total' => $total,
            'breakdown' => [
                'Package Type (' . ucfirst($packageType) . ')' => $baseCost,
                'Distance (' . $distanceKm . ' km)' => $distanceCost,
                'Weight (' . $weight . ' kg)' => $weightCost,
            ]
        ];
    }

    /**
     * Update pricing configuration
     *
     * @param array $config
     * @return void
     */
    public function updatePricing(array $config): void
    {
        if (isset($config['base_costs'])) {
            $this->baseCosts = array_merge($this->baseCosts, $config['base_costs']);
        }

        if (isset($config['cost_per_km'])) {
            $this->costPerKm = $config['cost_per_km'];
        }

        if (isset($config['cost_per_kg'])) {
            $this->costPerKg = $config['cost_per_kg'];
        }

        if (isset($config['minimum_cost'])) {
            $this->minimumCost = $config['minimum_cost'];
        }
    }

    /**
     * Get current pricing configuration
     *
     * @return array
     */
    public function getPricingConfig(): array
    {
        return [
            'base_costs' => $this->baseCosts,
            'cost_per_km' => $this->costPerKm,
            'cost_per_kg' => $this->costPerKg,
            'minimum_cost' => $this->minimumCost,
        ];
    }
}
