<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTAJet Courier Service - Setup Required</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
        .brand-orange {
            background-color: #F97316;
        }
        .brand-orange-text {
            color: #F97316;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl mx-auto px-6 text-center">
        
        <!-- Logo -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900">
                TTAJET.
                <span class="block text-lg font-normal text-gray-600">Courier Service</span>
            </h1>
        </div>
        
        <!-- Status Icon -->
        <div class="mb-8">
            <div class="w-24 h-24 brand-orange rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-cog fa-spin text-white text-3xl"></i>
            </div>
        </div>
        
        <!-- Message -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Application Setup Required</h2>
            <p class="text-gray-600 mb-6">
                The TTAJet Courier Service application is being set up. Please complete the following steps to get started:
            </p>
        </div>
        
        <!-- Setup Steps -->
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8 text-left">
            <h3 class="text-lg font-bold text-gray-900 mb-6">Setup Checklist:</h3>
            
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 brand-orange rounded-full flex items-center justify-center mt-0.5">
                        <i class="fas fa-check text-white text-xs"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-900">Laravel Project Structure</p>
                        <p class="text-sm text-gray-600">Basic Laravel files and directories have been created</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mt-0.5">
                        <i class="fas fa-clock text-white text-xs"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-900">Install Dependencies</p>
                        <p class="text-sm text-gray-600">Run <code class="bg-gray-100 px-2 py-1 rounded">composer install</code> in the ttajet directory</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mt-0.5">
                        <i class="fas fa-clock text-white text-xs"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-900">Environment Configuration</p>
                        <p class="text-sm text-gray-600">Copy <code class="bg-gray-100 px-2 py-1 rounded">.env.example</code> to <code class="bg-gray-100 px-2 py-1 rounded">.env</code> and configure database settings</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mt-0.5">
                        <i class="fas fa-clock text-white text-xs"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-900">Generate Application Key</p>
                        <p class="text-sm text-gray-600">Run <code class="bg-gray-100 px-2 py-1 rounded">php artisan key:generate</code></p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mt-0.5">
                        <i class="fas fa-clock text-white text-xs"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-gray-900">Database Migration</p>
                        <p class="text-sm text-gray-600">Run <code class="bg-gray-100 px-2 py-1 rounded">php artisan migrate --seed</code> to set up the database</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Commands -->
        <div class="bg-gray-900 text-white rounded-xl p-6 text-left">
            <h3 class="text-lg font-bold mb-4">Quick Setup Commands:</h3>
            <div class="space-y-2 font-mono text-sm">
                <p class="text-gray-300"># Navigate to the project directory</p>
                <p class="text-green-400">cd C:\xampp\htdocs\ttajet</p>
                
                <p class="text-gray-300 mt-4"># Install dependencies</p>
                <p class="text-green-400">composer install</p>
                
                <p class="text-gray-300 mt-4"># Set up environment</p>
                <p class="text-green-400">copy .env.example .env</p>
                
                <p class="text-gray-300 mt-4"># Generate application key</p>
                <p class="text-green-400">php artisan key:generate</p>
                
                <p class="text-gray-300 mt-4"># Run migrations and seed data</p>
                <p class="text-green-400">php artisan migrate --seed</p>
            </div>
        </div>
        
        <!-- Demo Access -->
        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p class="text-sm text-blue-800">
                <i class="fas fa-info-circle mr-2"></i>
                <strong>Demo Access:</strong> You can view the UI mockup at 
                <a href="/ttajet/uimockup.php" class="brand-orange-text hover:underline font-semibold">
                    /ttajet/uimockup.php
                </a>
            </p>
        </div>
        
        <!-- Footer -->
        <div class="mt-8 text-sm text-gray-500">
            <p>&copy; 2024 TTAJet Courier Service. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
