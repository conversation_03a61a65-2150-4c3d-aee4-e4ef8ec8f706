<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing data to use standardized status values
        $this->updateExistingStatusValues();

        // Then update the enum constraint to include new standardized values
        DB::statement("ALTER TABLE bookings MODIFY COLUMN status ENUM('pending', 'confirmed', 'rider_assigned', 'in_progress', 'delivered', 'cancelled', 'assigned', 'picked_up', 'in_transit') DEFAULT 'pending'");

        // Update any remaining old status values after enum expansion
        $this->updateExistingStatusValues();

        // Finally, remove old enum values to enforce standardization
        DB::statement("ALTER TABLE bookings MODIFY COLUMN status ENUM('pending', 'confirmed', 'rider_assigned', 'in_progress', 'delivered', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, expand enum to include old values
        DB::statement("ALTER TABLE bookings MODIFY COLUMN status ENUM('pending', 'confirmed', 'rider_assigned', 'in_progress', 'delivered', 'cancelled', 'assigned', 'picked_up', 'in_transit') DEFAULT 'pending'");

        // Revert status values to original format
        $this->revertStatusValues();

        // Finally, revert to original enum constraint
        DB::statement("ALTER TABLE bookings MODIFY COLUMN status ENUM('pending', 'assigned', 'picked_up', 'in_transit', 'delivered', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Update existing status values to standardized format
     */
    private function updateExistingStatusValues(): void
    {
        // Map old status values to new standardized ones
        $statusMappings = [
            'assigned' => 'rider_assigned',
            'picked_up' => 'in_progress',
            'in_transit' => 'in_progress',
            'delievered' => 'delivered', // Fix common misspelling
        ];

        foreach ($statusMappings as $oldStatus => $newStatus) {
            $count = DB::table('bookings')
                ->where('status', $oldStatus)
                ->update(['status' => $newStatus]);

            if ($count > 0) {
                Log::info("Updated {$count} bookings from status '{$oldStatus}' to '{$newStatus}'");
            }
        }
    }

    /**
     * Revert status values to original format (for rollback)
     */
    private function revertStatusValues(): void
    {
        // Map standardized status values back to original ones
        $revertMappings = [
            'rider_assigned' => 'assigned',
            'in_progress' => 'in_transit',
        ];

        foreach ($revertMappings as $newStatus => $oldStatus) {
            $count = DB::table('bookings')
                ->where('status', $newStatus)
                ->update(['status' => $oldStatus]);

            if ($count > 0) {
                Log::info("Reverted {$count} bookings from status '{$newStatus}' to '{$oldStatus}'");
            }
        }
    }
};
