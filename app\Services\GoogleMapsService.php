<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoogleMapsService
{
    protected $apiKey;
    protected $geocodingApiKey;
    protected $directionsApiKey;

    public function __construct()
    {
        $this->apiKey = config('services.google_maps.api_key');
        $this->geocodingApiKey = config('services.google_maps.geocoding_api_key', $this->apiKey);
        $this->directionsApiKey = config('services.google_maps.directions_api_key', $this->apiKey);
    }

    /**
     * Geocode an address to get latitude and longitude
     *
     * @param string $address
     * @return array
     * @throws \Exception
     */
    public function geocodeAddress(string $address): array
    {
        if (!$this->geocodingApiKey) {
            // Fallback to mock coordinates for Accra area if no API key
            return $this->getMockCoordinates($address);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'address' => $address,
                'key' => $this->geocodingApiKey,
                'region' => 'gh', // Ghana region bias
            ]);

            $data = $response->json();

            if ($data['status'] !== 'OK' || empty($data['results'])) {
                throw new \Exception('Unable to geocode address: ' . $address);
            }

            $location = $data['results'][0]['geometry']['location'];

            return [
                'lat' => $location['lat'],
                'lng' => $location['lng'],
                'formatted_address' => $data['results'][0]['formatted_address'],
            ];

        } catch (\Exception $e) {
            Log::error('Geocoding failed: ' . $e->getMessage());
            
            // Fallback to mock coordinates
            return $this->getMockCoordinates($address);
        }
    }

    /**
     * Calculate distance between two points using Google Maps Distance Matrix API
     *
     * @param float $originLat
     * @param float $originLng
     * @param float $destLat
     * @param float $destLng
     * @return float Distance in kilometers
     */
    public function calculateDistance(float $originLat, float $originLng, float $destLat, float $destLng): float
    {
        if (!$this->directionsApiKey) {
            // Fallback to haversine formula if no API key
            return $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', [
                'origins' => $originLat . ',' . $originLng,
                'destinations' => $destLat . ',' . $destLng,
                'units' => 'metric',
                'key' => $this->directionsApiKey,
            ]);

            $data = $response->json();

            if ($data['status'] !== 'OK' || 
                empty($data['rows']) || 
                $data['rows'][0]['elements'][0]['status'] !== 'OK') {
                
                // Fallback to haversine calculation
                return $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
            }

            $distanceMeters = $data['rows'][0]['elements'][0]['distance']['value'];
            return round($distanceMeters / 1000, 2); // Convert to kilometers

        } catch (\Exception $e) {
            Log::error('Distance calculation failed: ' . $e->getMessage());
            
            // Fallback to haversine calculation
            return $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
        }
    }

    /**
     * Calculate distance using Haversine formula (fallback)
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers
     */
    protected function calculateHaversineDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lng1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lng2);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c, 2);
    }

    /**
     * Get mock coordinates for testing (Accra area)
     *
     * @param string $address
     * @return array
     */
    protected function getMockCoordinates(string $address): array
    {
        // Generate random coordinates within Accra area
        $accraLat = 5.6037;
        $accraLng = -0.1870;
        
        // Add some randomness within ~10km radius
        $latOffset = (rand(-100, 100) / 1000) * 0.1;
        $lngOffset = (rand(-100, 100) / 1000) * 0.1;

        return [
            'lat' => $accraLat + $latOffset,
            'lng' => $accraLng + $lngOffset,
            'formatted_address' => $address . ', Accra, Ghana',
        ];
    }

    /**
     * Get directions between two points
     *
     * @param float $originLat
     * @param float $originLng
     * @param float $destLat
     * @param float $destLng
     * @return array
     */
    public function getDirections(float $originLat, float $originLng, float $destLat, float $destLng): array
    {
        if (!$this->directionsApiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/directions/json', [
                'origin' => $originLat . ',' . $originLng,
                'destination' => $destLat . ',' . $destLng,
                'key' => $this->directionsApiKey,
            ]);

            return $response->json();

        } catch (\Exception $e) {
            Log::error('Directions API failed: ' . $e->getMessage());
            return ['error' => 'Failed to get directions'];
        }
    }
}
